import 'app_l10n.dart';

// ignore_for_file: type=lint

/// The translations for German (`de`).
class AppL10nDe extends AppL10n {
  AppL10nDe([String locale = 'de']) : super(locale);

  @override
  String get deskTabWidgets => 'Widget-Sammlung';

  @override
  String get deskTabPainter => 'Zeichnungssammlung';

  @override
  String get deskTabKnowledge => 'Wissensdatenbank';

  @override
  String get deskTabTools => 'Werkzeugkasten';

  @override
  String get deskTabMine => 'App-Info';

  @override
  String get messageBoard => 'Pinnwand';

  @override
  String get deskTabWyHome => '位移计主页';

  @override
  String get deskTabDeviceVideo => '位移计视频';

  @override
  String get mobileTabWidgets => 'Widgets';

  @override
  String get mobileTabPainter => 'Malen';

  @override
  String get mobileTabKnowledge => 'Wissen';

  @override
  String get mobileTabTools => 'Werkzeuge';

  @override
  String get mobileTabMine => 'Mein';

  @override
  String get mobileTabWyHome => '位移计主页';

  @override
  String get mobileTabDeviceVideo => '位移计视频';
}
