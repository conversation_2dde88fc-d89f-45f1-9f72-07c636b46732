{"id": 17, "name": "CheckboxListTile", "localName": "Azulejo de Seleção", "info": "Uma estrutura de item de lista genérica fornecida pelo Flutter, com uma estrutura esquerda-central, e uma CheckBox na extremidade. Componentes podem ser inseridos nas posições correspondentes, permitindo uma fácil adaptação a itens específicos.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "A apresentação básica do CheckBoxListTile é a seguinte", "desc": ["【secondary】: Componente do lado esquerdo   【Widget】", "【checkColor】: Cor do ✔️   【Color】", "【activeColor】: Co<PERSON> da moldura quando selecionado   【Color】", "【title】: Componente superior central   【Widget】", "【subtitle】: Componente inferior central   【Widget】", "【onChanged】: Evento de seleção   【Function(bool)】"]}, {"file": "node2_select.dart", "name": "Efeito de seleção do CheckBoxListTile", "desc": ["【selected】: Se está selecionado   【bool】"]}, {"file": "node3_dense.dart", "name": "Propriedade de densidade do CheckBoxListTile", "desc": ["【dense】: Se está em alta densidade   【bool】"]}]}