{"id": 356, "name": "Autocomplete", "localName": "オートコンプリート", "info": "入力中に、ユーザーが選択できるように連想語をフローティングレイヤーで表示し、高いカスタマイズ性を持っています。", "lever": 4, "family": 0, "linkIds": [54, 199], "nodes": [{"file": "node1_base.dart", "name": "Autocompleteの基本使用", "desc": ["【optionsBuilder】 : オプション構築器   【AutocompleteOptionsBuilder<T>】", "【onSelected】 : 選択時のコールバック   【AutocompleteOnSelected<T>】"]}, {"file": "node2_type.dart", "name": "Autocompleteのジェネリック", "desc": ["【optionsViewBuilder】 : パネル構築器   【AutocompleteOptionsViewBuilder<T>】", "【fieldViewBuilder】 : 入力構築器   【AutocompleteFieldViewBuilder】", "【displayStringForOption】 : テキスト表示   【AutocompleteOptionToString】,"]}]}