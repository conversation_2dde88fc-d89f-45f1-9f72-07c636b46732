{"id": 12, "name": "ChoiceChip", "localName": "Pestaña de selección", "info": "Estilo similar al componente Chip, con algunas propiedades de selección. Puede especificar el color cuando está seleccionado, el color de la sombra y el evento de selección.", "lever": 3, "family": 0, "linkIds": [11, 13, 14, 15, 153], "nodes": [{"file": "node1_base.dart", "name": "El comportamiento normal de ChoiceChip es el siguiente", "desc": ["【selectedColor】: Color cuando está seleccionado   【Color】", "【selectedShadowColor】: Color de la sombra cuando está seleccionado   【Color】", "【onSelected】: Evento de selección   【Fuction(bool)】", "    <PERSON><PERSON> propiedades son las mismas que el componente Chip, sin componente a la derecha."]}]}