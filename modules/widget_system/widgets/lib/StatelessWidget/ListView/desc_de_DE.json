{"id": 162, "name": "ListView", "localName": "Listenkomponente", "info": "Führende Figur bei der Anzeige von Listen, die mehrere Unterkomponenten aufnehmen kann und über Builder, Separated, Custom usw. konstruiert werden kann. Verfügt über Eigenschaften wie Innenabstand, Umkehrung, Scroll-Controller usw.", "lever": 5, "family": 0, "linkIds": [16, 163], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von <PERSON>", "desc": ["【children】 : Liste der Unterkomponenten   【List<Widget>】", "【padding】 : Innenabstand  【EdgeInsetsGeometry】"]}, {"file": "node2_direction.dart", "name": "Horizontales Scrollen mit ListView", "desc": ["【scrollDirection】 : Scrollrichtung   【Axis】", "【reverse】 : Umgekehrtes Scrollen   【bool】", "【shrinkWrap】 : Be<PERSON> fehlenden Grenzen e<PERSON>wickeln  【bool】"]}, {"file": "node3_builder.dart", "name": "ListView.builder Konstruktion", "desc": ["【itemCount】 : <PERSON><PERSON><PERSON> der Elemente   【int】", "【itemBuilder】 : Elementkonstruktor   【IndexedWidgetBuilder】"]}, {"file": "node4_separated.dart", "name": "ListView.separated Konstruktion", "desc": ["【separatorBuilder】 : Elementkonstruktor   【IndexedWidgetBuilder】"]}]}