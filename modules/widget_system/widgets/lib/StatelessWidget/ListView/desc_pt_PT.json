{"id": 162, "name": "ListView", "localName": "Componente de Lista", "info": "O líder na exibição de listas, acomoda vários subcomponentes, pode ser construído através de builder, separated, custom, etc. Possui atributos como padding, reversão, controlador de deslizamento, etc.", "lever": 5, "family": 0, "linkIds": [16, 163], "nodes": [{"file": "node1_base.dart", "name": "Uso básico do ListView", "desc": ["【children】 : Lista de subcomponentes   【List<Widget>】", "【padding】 : Padding   【EdgeInsetsGeometry】"]}, {"file": "node2_direction.dart", "name": "Deslizamento horizontal do ListView", "desc": ["【scrollDirection】 : Direção de deslizamento   【Axis】", "【reverse】 : Deslizamento reverso   【bool】", "【shrinkWrap】 : Envolver quando sem limites   【bool】"]}, {"file": "node3_builder.dart", "name": "Construção do ListView.builder", "desc": ["【itemCount】 : <PERSON><PERSON>mer<PERSON> de itens   【int】", "【itemBuilder】 : Construtor de itens   【IndexedWidgetBuilder】"]}, {"file": "node4_separated.dart", "name": "Construção do ListView.separated", "desc": ["【separatorBuilder】 : Construtor de itens   【IndexedWidgetBuilder】"]}]}