{"id": 142, "name": "BottomSheet", "localName": "Cassetto inferiore", "info": "Come componente, è una struttura semplice che può specificare forma, profondità dell'ombra, colore di sfondo, costruttore di componenti interni. Di solito viene visualizzato dal basso tramite il metodo showBottomSheet di ScaffoldState.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di BottomSheet", "desc": ["【builder】 : Costru<PERSON><PERSON> del componente   【WidgetBuilder】", "【backgroundColor】 : Colore di sfondo   【Color】", "【elevation】 : Profondità dell'ombra   【double】", "【shape】 : Forma   【ShapeBorder】", "【onClosing】 : Callback di chiusura  【Function()】"]}]}