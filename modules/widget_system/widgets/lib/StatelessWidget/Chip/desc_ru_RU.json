{"id": 11, "name": "Chip", "localName": "Маленький компонент", "info": "Горизонтальная полоска с закругленными краями, которая может содержать три компонента: левый, центральный и правый. Можно указать цвет, цвет тени и событие клика.", "lever": 4, "family": 0, "linkIds": [12, 13, 14, 15, 153], "nodes": [{"file": "node1_base.dart", "name": "Обычное отображение Chip", "desc": ["【avatar】: Левый компонент   【Widget】", "【label】: Центральный компонент   【Widget】", "【padding】: Внутренние отступы   【EdgeInsetsGeometry】", "【labelPadding】: Отступы для label   【EdgeInsetsGeometry】"]}, {"file": "node2_color.dart", "name": "Можно задать цвет и тень", "desc": ["【backgroundColor】: Цвет фона   【Color】", "【shadowColor】: Цвет тени   【Color】", "【elevation】: Г<PERSON><PERSON><PERSON><PERSON>на тени   【double】"]}, {"file": "node3_delete.dart", "name": "Можно задать кнопку клика справа", "desc": ["【deleteIcon】: Правый компонент (обычно Icon)   【Widget】", "【deleteIconColor】: Цвет правого компонента   【Color】", "【onDeleted】: Событие клика правого компонента   【Function】"]}]}