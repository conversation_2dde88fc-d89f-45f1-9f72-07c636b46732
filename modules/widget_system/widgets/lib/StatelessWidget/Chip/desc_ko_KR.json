{"id": 11, "name": "Chip", "localName": "작은 막대 컴포넌트", "info": "가로로 된 둥근 모서리의 작은 막대로, 왼쪽, 중앙, 오른쪽 세 개의 컴포넌트를 포함할 수 있습니다. 색상, 그림자 색상 및 클릭 이벤트를 지정할 수 있습니다.", "lever": 4, "family": 0, "linkIds": [12, 13, 14, 15, 153], "nodes": [{"file": "node1_base.dart", "name": "Chip의 일반적인 모습은 다음과 같습니다", "desc": ["【avatar】: 왼쪽 컴포넌트   【Widget】", "【label】: 중앙 컴포넌트   【Widget】", "【padding】 : 안쪽 여백  【EdgeInsetsGeometry】", "【labelPadding】: label 여백   【EdgeInsetsGeometry】"]}, {"file": "node2_color.dart", "name": "색상과 그림자를 설정할 수 있습니다", "desc": ["【backgroundColor】: 배경색   【Color】", "【shadowColor】: 그림자 색상   【Color】", "【elevation】: 그림자 깊이   【double】"]}, {"file": "node3_delete.dart", "name": "오른쪽 클릭 버튼을 설정할 수 있습니다", "desc": ["【deleteIcon】: 오른쪽 컴포넌트(일반적으로 Icon)   【Widget】", "【deleteIconColor】: 오른쪽 컴포넌트 색상   【Color】", "【onDeleted】: 오른쪽 컴포넌트 클릭 이벤트   【Function】"]}]}