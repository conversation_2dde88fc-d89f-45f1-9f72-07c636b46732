{"id": 216, "name": "CupertinoPageTransition", "localName": "页面过渡变换", "info": "提供一个 iOS 风格的页面过渡动画变换。 ", "lever": 1, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "CupertinoPageTransition 介绍", "desc": ["【child】 : 子组件   【Widget】", "【linearTransition】 : 是否线性转换   【bool】", "【primaryRouteAnimation】 : 初始路由动画   【Animation<double>】", "【secondaryRouteAnimation】 : 第二路由动画   【Animation<double>】"]}]}