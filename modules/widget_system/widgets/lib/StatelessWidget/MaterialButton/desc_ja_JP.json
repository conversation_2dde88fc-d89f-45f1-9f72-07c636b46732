{"id": 23, "name": "MaterialButton", "localName": "マテリアルボタン", "info": "RawMaterialButtonをベースにした汎用Materialボタンです。子コンポーネントを保持でき、色や形状などの表現を定義でき、クリックや長押しイベントを受け取ることができます。", "lever": 4, "family": 0, "linkIds": [25, 26, 27, 326, 175], "nodes": [{"file": "node1_base.dart", "name": "MaterialButtonクリックイベント", "desc": ["【color】: 色   【Color】", "【splashColor】: 波紋色   【Color】", "【height】: 高さ   【double】", "【elevation】: 影の深さ   【double】", "【child】: 子コンポーネント   【Widget】", "【textColor】: 子コンポーネントのテキスト色   【Color】", "【highlightColor】: 長押しハイライト色   【Color】", "【padding】: パディング   【EdgeInsetsGeometry】", "【onPressed】: クリックイベント   【Function】"]}, {"file": "node2_onLongPress.dart", "name": "MaterialButton長押しイベント", "desc": ["【highlightColor】: 長押しハイライト色   【Color】", "【onLongPress】: 長押しイベント   【Function】"]}, {"file": "node3_shape.dart", "name": "MaterialButtonのカスタム形状", "desc": ["【shape】: 形状   【ShapeBorder】"]}]}