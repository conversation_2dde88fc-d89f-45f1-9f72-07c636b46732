{"id": 141, "name": "SnackBarAction", "localName": "Кнопка информационной панели", "info": "Обычно используется только в SnackBar, принимает события нажатия. После одного нажатия кнопка становится отключенной, можно указать цвет и цвет в отключенном состоянии.", "lever": 1, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование SnackBarAction", "desc": ["【label】 :  Метка  【String】", "【textColor】 : Цвет текста   【Color】", "【disabledTextColor】 : Цвет текста в отключенном состоянии   【Color】", "【onPressed】 : Обратный вызов при нажатии  【Function()】"]}]}