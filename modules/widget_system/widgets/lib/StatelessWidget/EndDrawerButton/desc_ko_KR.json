{"id": 361, "name": "EndDrawerButton", "localName": "오른쪽 서랍 버튼", "info": "오른쪽 서랍 아이콘 버튼, EndDrawerButtonIcon을 사용하여 아이콘을 표시하며, 기본 클릭 이벤트는 오른쪽 서랍을 열 수 있습니다.", "lever": 1, "family": 0, "linkIds": [275, 276], "nodes": [{"file": "node1_base.dart", "name": "EndDrawerButton 기본 사용", "desc": ["【onPressed】 : 클릭 이벤트  【VoidCallback?】", "【style】: 버튼 스타일   【ButtonStyle?】", "onPressed가 비어 있을 때, 클릭하면 오른쪽 서랍이 열립니다."]}]}