{"id": 258, "name": "Badge", "localName": "Badge Component", "info": "Badge is a Material-style component that can add markers on top of the child. It relies on the Stack component for layering during construction.", "lever": 2, "family": 1, "linkIds": [], "nodes": [{"file": "node_1.dart", "name": "Usage of Badge Dot Marker", "desc": ["【backgroundColor】: Background color 【Color?】", "【isLabelVisible】: Whether to display the marker 【bool】", "【smallSize】: Diameter when there is no label 【double?】", "【child】: Child component 【Widget?】"]}, {"file": "node_2.dart", "name": "Badge Label Marker", "desc": ["【label】: Title component 【Widget?】", "【textStyle】: Title color 【TextStyle?】", "【textColor】: Title style 【Color?】", "【padding】: Title margin 【EdgeInsetsGeometry?】", "【largeSize】: Height when there is a label 【double?】"]}, {"file": "node_3.dart", "name": "Badge Offset", "desc": ["【offset】: Marker offset 【Offset?】", "【alignment】: Title offset 【AlignmentDirectional?】"]}]}