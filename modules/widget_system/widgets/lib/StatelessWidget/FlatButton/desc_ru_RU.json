{"id": 25, "name": "FlatButton", "localName": "Плоская кнопка", "info": "Плоская кнопка без тени, реализованная на основе MaterialButton, все свойства аналогичны MaterialButton.", "lever": 3, "family": 0, "deprecated": -1, "linkIds": [24, 26, 27, 175], "nodes": [{"file": "node1_base.dart", "name": "Событие нажатия FlatButton", "desc": ["【color】: Цвет   【Color】", "【splashColor】: Цвол водной ряби   【Color】", "【child】: До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【textColor】: Цвет текста дочернего компонента   【Color】", "【highlightColor】: Цвет подсветки при длительном нажатии   【Color】", "【padding】: Внутренний отступ   【EdgeInsetsGeometry】", "【onPressed】: Событие нажатия   【Function】", "    ", "", "class CustomFlatButton extends StatelessWidget {", "  const CustomFlatButton({Key? key) : super(key: key);", "", "  final String info =", "      'Кнопка FlatButton ушла с исторической сцены в Flutter3.3. Ее заменила кнопка ElevatedButton.';", "", "  @override", "  Widget build(BuildContext context) {", "    return Container(", "      color: Colors.blue.withOpacity(0.1),", "      padding: const EdgeInsets.all(10),", "      margin: const EdgeInsets.all(10),", "      child: Text(info),", "    );", "  ", "", " @override", " Widget build(BuildContext context) {", "   return FlatButton(", "     onPressed: ()=>{,", "     padding: const EdgeInsets.all(8),", "     splashColor: Colors.green,", "     child: const Text(\"FlatButton\"),", "     textColor: const Color(0xffFfffff),", "     color: Colors.blue,", "     highlightColor: const Color(0xffF88B0A),"]}]}