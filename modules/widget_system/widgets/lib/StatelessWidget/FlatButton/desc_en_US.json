{"id": 25, "name": "FlatButton", "localName": "Flat Button", "info": "A flat button without shadows, implemented based on MaterialButton, with all properties similar to MaterialButton.", "lever": 3, "family": 0, "deprecated": -1, "linkIds": [24, 26, 27, 175], "nodes": [{"file": "node1_base.dart", "name": "FlatButton Click Event", "desc": ["【color】: Color   【Color】", "【splashColor】: Splash Color   【Color】", "【child】: Child Widget   【Widget】", "【textColor】: Text Color of Child Widget   【Color】", "【highlightColor】: Long Press Highlight Color   【Color】", "【padding】: Padding   【EdgeInsetsGeometry】", "【onPressed】: Click Event   【Function】", "    ", "", "class CustomFlatButton extends StatelessWidget {", "  const CustomFlatButton({Key? key) : super(key: key);", "", "  final String info =", "      'FlatButton was phased out in Flutter 3.3. Its successor is the ElevatedButton.';", "", "  @override", "  Widget build(BuildContext context) {", "    return Container(", "      color: Colors.blue.withOpacity(0.1),", "      padding: const EdgeInsets.all(10),", "      margin: const EdgeInsets.all(10),", "      child: Text(info),", "    );", "  ", "", " @override", " Widget build(BuildContext context) {", "   return FlatButton(", "     onPressed: ()=>{,", "     padding: const EdgeInsets.all(8),", "     splashColor: Colors.green,", "     child: const Text(\"FlatButton\"),", "     textColor: const Color(0xffFfffff),", "     color: Colors.blue,", "     highlightColor: const Color(0xffF88B0A),"]}]}