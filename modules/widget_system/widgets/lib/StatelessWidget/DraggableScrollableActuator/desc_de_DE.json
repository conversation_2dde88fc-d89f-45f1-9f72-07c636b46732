{"id": 221, "name": "DraggableScrollableActuator", "localName": "Zieh- und Schieb-Rücksetzer", "info": "Es kann die nachfolgenden DraggableScrollableSheet benachrichtigen, um ihre Position auf den Ausgangszustand zurückzusetzen.", "lever": 1, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung", "desc": ["【child】 : Untergeordnete Komponente   【Widget】", "Verwenden Sie DraggableScrollableActuator.reset(context), um die Position der nachfolgenden DraggableScrollableSheet auf die Ausgangsposition zurückzusetzen."]}]}