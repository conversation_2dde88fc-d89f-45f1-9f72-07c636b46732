{"id": 15, "name": "FilterChip", "localName": "Фильтр-чип", "info": "Стиль, аналогичный компоненту Chip, с атрибутом выбора и событием выбора. При выборе верхний слой левого компонента будет затенен ✔️.", "lever": 4, "family": 0, "linkIds": [11, 12, 13, 14, 153], "nodes": [{"file": "node1_base.dart", "name": "FilterChip может принимать события выбора", "desc": ["【selected】: Выбрано ли   【bool】", "【onSelected】: Событие выбора   【Function(bool)】", "【selectedColor】: Цвет после выбора   【Color】", "【selectedShadowColor】: Цвет тени после выбора   【Color】,"]}]}