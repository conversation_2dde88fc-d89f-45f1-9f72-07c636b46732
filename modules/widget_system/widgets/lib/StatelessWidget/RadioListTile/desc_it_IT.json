{"id": 19, "name": "RadioListTile", "localName": "Piastrella Radio", "info": "Una struttura di elenco generica fornita da Flutter, con una struttura centrale-destra, e una Radio alla fine. I componenti possono essere inseriti nelle posizioni corrispondenti, rendendo facile adattarsi a voci specifiche.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "RadioListTile richiede un tipo generico T", "desc": ["【value】 : oggetto voce  【T】", "【groupValue】 : oggetto selezionato  【T】", "【selected】: se selezionato   【bool】", "【secondary】: componente a destra   【Widget】", "【title】: componente superiore centrale   【Widget】", "【subtitle】: componente inferiore centrale   【Widget】", "【onChanged】: evento di cambio   【Function(T)】"]}, {"file": "node2_dense.dart", "name": "Colore selezionato e disposizione compatta di RadioListTile", "desc": ["【activeColor】 : colore quando selezionato  【Color】", "【dense】: se disposto in modo compatto   【bool】"]}]}