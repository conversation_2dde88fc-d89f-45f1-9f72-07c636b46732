{"id": 220, "name": "NotificationListener", "localName": "알림 리스너", "info": "Notification의 하위 제네릭 T를 지정하여 해당 유형의 변경 사항을 감지할 수 있습니다. Flutter에는 많은 스크롤 Notification이 내장되어 있으며, 물론 사용자 정의 Notification을 감지할 수도 있습니다.", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "OverscrollIndicatorNotification 감지", "desc": ["이 알림은 스크롤이 가장 위나 가장 아래에 도달했을 때 콜백되며, leading 속성을 통해 상단인지 하단인지 판단할 수 있습니다. 또한 notification#disallowGlow()를 통해 상하단 스크롤 시 파란색 그림자를 제거할 수 있습니다."]}, {"file": "node2_update.dart", "name": "ScrollUpdateNotification 감지", "desc": ["스크롤 과정에서 스크롤 데이터를 콜백하며, 이를 통해 많은 데이터를 얻어 작업할 수 있습니다."]}]}