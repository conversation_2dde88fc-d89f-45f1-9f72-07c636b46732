{"id": 220, "name": "NotificationListener", "localName": "Ascoltatore di Notifiche", "info": "È possibile specificare il sottotipo generico T di Notification per monitorare i cambiamenti di quel tipo. Flutter include molte notifiche di scorrimento integrate, ma puoi anche creare e monitorare notifiche personalizzate.", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Monitorare OverscrollIndicatorNotification", "desc": ["Questa notifica viene richiamata solo quando si scorre fino in cima o in fondo, e si può determinare se si tratta della cima o del fondo tramite la proprietà leading. Inoltre, è possibile rimuovere l'ombra blu dello scorrimento in cima e in fondo utilizzando notification#disallowGlow()."]}, {"file": "node2_update.dart", "name": "Monitorare ScrollUpdateNotification", "desc": ["Durant<PERSON> lo scorrimento, i dati di scorrimento vengono richiamati, permettendoti di ottenere molti dati per operazioni successive."]}]}