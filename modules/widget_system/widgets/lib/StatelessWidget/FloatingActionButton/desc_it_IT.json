{"id": 28, "name": "FloatingActionButton", "localName": "Pulsante galleggiante", "info": "Il pulsante galleggiante, generalmente utilizzato in Scaffold, può essere posizionato in una posizione specifica. Può contenere un componente figlio, ricevere clic, definire colori, forme, ecc.", "lever": 4, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Evento di clic di FloatingActionButton", "desc": ["【child】: Componente figlio   【Widget】", "【tooltip】: Testo di suggerimento durante la pressione prolungata   【String】", "【backgroundColor】: Colore di sfondo   【Color】", "【foregroundColor】: Colore di primo piano   【Color】", "【elevation】: Profondità dell'ombra   【double】", "【onPressed】: Evento di clic   【Function】"]}, {"file": "node2_mini.dart", "name": "Proprietà mini", "desc": ["【mini】: Se è mini   【bool】"]}, {"file": "node3_shape.dart", "name": "Proprietà shape", "desc": ["【shape】: Forma   【ShapeBorder】"]}]}