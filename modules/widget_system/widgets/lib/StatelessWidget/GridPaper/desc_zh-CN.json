{"id": 37, "name": "GridPaper", "localName": "网格线组件", "info": "可容纳一个组件，在其上绘制网格。可指定颜色、线宽、间距等属性。", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "GridPager 基础属性", "desc": ["【child】: 子组件   【Widget】", "【color】: 颜色   【Color】", "【interval】: 小块边长   【double】"]}, {"file": "node2_divisions.dart", "name": "GridPager 再分割", "desc": ["【child】: 子组件   【Widget】", "【color】: 颜色   【Color】", "【subdivisions】: 小块中子块个数   【int】", "【divisions】: 小块中子块的分割数   【int】"]}]}