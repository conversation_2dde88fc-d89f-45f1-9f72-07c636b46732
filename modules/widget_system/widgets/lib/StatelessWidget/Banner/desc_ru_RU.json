{"id": 5, "name": "Banner", "localName": "Компонент для отображения уголка", "info": "Компонент для отображения уголка. Может содержать один дочерний компонент, позволяет выбрать место для добавления уголка и текстовой информации, можно настроить цвет.", "lever": 1, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Для отображения уголка", "desc": ["【message】 : Отображаемое текстовое сообщение  【String】", "【location】 : Позиция*4  【BannerLocation】", "【color】: Цвет уголка   【Color】", "【child】: До<PERSON><PERSON><PERSON><PERSON>ий элемент   【Widget】", "【textStyle】: Стиль текста   【TextStyle】"]}]}