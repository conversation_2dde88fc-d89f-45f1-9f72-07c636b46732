{"id": 163, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "localName": "Componente griglia", "info": "Contiene più componenti e li organizza in una griglia. Può essere costruito utilizzando count, extent, custom, builder, ecc. Ha proprietà come padding, reverse, controller di scorrimento, ecc.", "lever": 5, "family": 0, "linkIds": [21, 162], "nodes": [{"file": "node1_base.dart", "name": "Costruzione GridView.count", "desc": ["【children】 : Lista di componenti figli   【List<Widget>】", "【crossAxisCount】 : Numero di box per riga nell'asse principale  【int】", "【mainAxisSpacing】 : Spaziatura tra le righe nell'asse principale  【double】", "【crossAxisSpacing】 : Spaziatura tra le righe nell'asse trasversale  【double】", "【childAspectRatio】 : Rapporto lunghezza principale/lunghezza trasversale del box  【double】", "【crossAxisCount】 : Numero di elementi per riga nell'asse principale  【int】"]}, {"file": "node2_direction.dart", "name": "Direzione di scorrimento GridView", "desc": ["【scrollDirection】 : Direzione di scorrimento   【Axis】", "【reverse】 : Scorrimento inverso   【bool】", "【shrinkWrap】 : Avvolgere quando non ci sono confini  【bool】"]}, {"file": "node3_extend.dart", "name": "Direzione di scorrimento GridView", "desc": ["【scrollDirection】 : Direzione di scorrimento   【Axis】", "【reverse】 : Scorrimento inverso   【bool】", "【shrinkWrap】 : Avvolgere quando non ci sono confini  【bool】"]}, {"file": "node4_builder.dart", "name": "Costruzione GridView.builder", "desc": ["【itemCount】 : Numero di elementi   【int】", "【gridDelegate】 : Delegato della griglia   【SliverGridDelegate】", "【itemBuilder】 : Costruttore di elementi  【IndexedWidgetBuilder】"]}]}