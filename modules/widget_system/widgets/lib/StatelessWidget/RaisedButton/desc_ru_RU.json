{"id": 26, "name": "RaisedButton", "localName": "Кнопка с тенью", "info": "Кнопка с тенью, реализованная на основе MaterialButton, все свойства аналогичны MaterialButton.", "lever": 3, "family": 0, "deprecated": -1, "linkIds": [24, 25, 27, 175], "nodes": [{"file": "node1_base.dart", "name": "Событие нажатия RaisedButton", "desc": ["【color】: Цвет   【Color】", "【splashColor】: Цвол водной ряби   【Color】", "【elevation】: Г<PERSON><PERSON><PERSON><PERSON>на тени   【double】", "【child】: До<PERSON><PERSON><PERSON><PERSON><PERSON> виджет   【Widget】", "【textColor】: Цвет текста дочернего виджета   【Color】", "【highlightColor】: Цвет подсветки при длительном нажатии   【Color】", "【padding】: Внутренние отступы   【EdgeInsetsGeometry】", "【onPressed】: Событие нажатия   【Function】", "    ", "", "class CustomRaisedButton extends StatelessWidget {", "  const CustomRaisedButton({Key? key) : super(key: key);", "", "  final String info =", "      'Кнопка RaisedButton ушла с исторической сцены в Flutter 3.3. Её заменила кнопка ElevatedButton.';", "", "  @override", "  Widget build(BuildContext context) {", "    return Container(", "      color: Colors.blue.withOpacity(0.1),", "      padding: const EdgeInsets.all(10),", "      margin: const EdgeInsets.all(10),", "      child: Text(info),", "    );", "  ", "", "   @override", "   Widget build(BuildContext context) {", "     return RaisedButton(", "       color: Colors.blue,", "       splashColor: Colors.green,", "       onPressed: () {,", "       child: const Text(\"RaisedButton\"),", "       textColor: const Color(0xffFfffff),", "       padding: const EdgeInsets.all(8),", "       elevation: 5,", "       highlightColor: const Color(0xffF88B0A),"]}]}