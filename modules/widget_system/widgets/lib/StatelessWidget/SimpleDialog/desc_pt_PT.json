{"id": 128, "name": "SimpleDialog", "localName": "Diálogo Simples", "info": "Uma estrutura de diálogo simples que permite especificar componentes no topo e no meio. Possui estilos de texto e margens para o título e conteúdo, além de propriedades como sombra, forma, etc. Geralmente usado em conjunto com SimpleDialogOption.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do SimpleDialog", "desc": ["【title】 : Componente do topo   【Widget】", "【children】 : Lista de componentes filhos  【List<Widget>】", "【titlePadding】 : Preenchimento interno do topo  【EdgeInsetsGeometry】", "【contentPadding】 : Preenchimento interno do conteúdo  【EdgeInsetsGeometry】", "【backgroundColor】 : Cor de fundo  【Color】", "【elevation】 : Profundidade da sombra  【double】", "【shape】 : Forma   【ShapeBorder】"]}]}