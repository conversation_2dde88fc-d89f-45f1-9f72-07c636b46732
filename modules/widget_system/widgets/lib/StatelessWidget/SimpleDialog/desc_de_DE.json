{"id": 128, "name": "SimpleDialog", "localName": "Einfacher Dialog", "info": "Eine einfache Dialogstruktur, die Kopf- und Mittelkomponenten angeben kann. Verfügt über Textstile und Ränder für Titel und Inhalt sowie Eigenschaften wie Schattentiefe und Form. Wird oft mit SimpleDialogOption verwendet.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von SimpleDialog", "desc": ["【title】 : Oberste Komponente   【Widget】", "【children】 : Liste der Unterkomponenten  【List<Widget>】", "【titlePadding】 : Oberer Innenabstand  【EdgeInsetsGeometry】", "【contentPadding】 : Innenabstand des Inhalts  【EdgeInsetsGeometry】", "【backgroundColor】 : Hintergrundfarbe  【Color】", "【elevation】 : Sc<PERSON>tentiefe  【double】", "【shape】 : Form   【ShapeBorder】"]}]}