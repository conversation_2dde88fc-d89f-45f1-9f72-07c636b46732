{"id": 3, "name": "Card", "localName": "Composant Carte", "info": " Basé sur les composants Material, utilisé pour transformer un composant unique en carte. Il peut avoir un effet d'ombre, peut ajouter des marges extérieures, et peut également personnaliser la forme de la carte.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Card peut transformer un composant en carte", "desc": ["【elevation】 : profondeur de l'ombre  【double】", "【margin】: marge extérieure   【double】", "【color】: couleur   【Color】", "【child】: enfant   【Widget】"]}, {"file": "node2_shape.dart", "name": "Peut réaliser un effet de découpe via la propriété shape", "desc": ["【shape】 : forme  【ShapeBorder】", "【margin】: marge extérieure   【double】", "【color】: couleur   【Color】", "【child】: enfant   【Widget】"]}]}