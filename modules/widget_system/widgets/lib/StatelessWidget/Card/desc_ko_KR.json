{"id": 3, "name": "Card", "localName": "카드 컴포넌트", "info": "Material 컴포넌트를 기반으로 구현되었으며, 단일 컴포넌트를 카드화하는 데 사용됩니다. 그림자 효과를 가지며, 외부 여백을 추가할 수 있고, 카드 모양을 사용자 정의할 수도 있습니다.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Card는 컴포넌트를 카드화할 수 있습니다", "desc": ["【elevation】 : 그림자 깊이  【double】", "【margin】: 외부 여백   【double】", "【color】: 색상   【Color】", "【child】: 자식   【Widget】"]}, {"file": "node2_shape.dart", "name": "shape 속성을 통해 잘라내기 효과를 구현할 수 있습니다", "desc": ["【shape】 : 모양  【ShapeBorder】", "【margin】: 외부 여백   【double】", "【color】: 색상   【Color】", "【child】: 자식   【Widget】"]}]}