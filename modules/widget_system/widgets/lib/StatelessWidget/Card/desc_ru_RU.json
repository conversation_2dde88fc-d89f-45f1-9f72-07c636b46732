{"id": 3, "name": "Card", "localName": "Карточка", "info": "Реализовано на основе компонентов Material, используется для карточного оформления отдельного компонента. Имеет эффект тени, можно добавить внешние отступы, также можно настроить форму карточки.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Card может сделать компонент карточным", "desc": ["【elevation】 : Г<PERSON><PERSON><PERSON><PERSON>на тени  【double】", "【margin】: Вне<PERSON>ний отступ   【double】", "【color】: Цвет   【Color】", "【child】: До<PERSON><PERSON><PERSON><PERSON>ий элемент   【Widget】"]}, {"file": "node2_shape.dart", "name": "Можно реализовать эффект обрезки с помощью свойства shape", "desc": ["【shape】 : Форма  【ShapeBorder】", "【margin】: Вне<PERSON>ний отступ   【double】", "【color】: Цвет   【Color】", "【child】: До<PERSON><PERSON><PERSON><PERSON>ий элемент   【Widget】"]}]}