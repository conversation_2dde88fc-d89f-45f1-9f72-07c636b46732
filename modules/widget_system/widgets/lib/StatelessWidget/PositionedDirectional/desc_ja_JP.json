{"id": 159, "name": "PositionedDirectional", "localName": "方向定位", "info": "Positionedコンポーネントと同じ機能で、属性名が異なります。Stack内でのみ使用でき、左上右下の距離を指定してコンポーネントを正確に配置できます。", "lever": 3, "family": 0, "linkIds": [108, 122], "nodes": [{"file": "node1_base.dart", "name": "PositionedDirectional基本使用", "desc": ["【child】 : コンポーネント   【Widget】", "【top】 : 親の上端までの距離   【double】", "【end】 : 親の右端までの距離   【double】", "【start】 : 親の左端までの距離   【double】", "【bottom】 : 親の下端までの距離   【double】"]}]}