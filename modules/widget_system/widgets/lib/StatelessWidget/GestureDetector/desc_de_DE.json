{"id": 146, "name": "GestureDetector", "localName": "Gesten-Erkenner", "info": "Komponente zur Erkennung von <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Dr<PERSON><PERSON>, Loslassen, Bewegen usw. erkennen und kann Informationen über die Berührungspunkte abrufen. Ein unverzichtbares Werkzeug für zu Hause und unterwegs.", "lever": 5, "family": 0, "linkIds": [147, 150], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Ereignisse von GestureDetector", "desc": ["【child】 : Untergeordnete Komponente   【Widget】", "【onTap】 : Klick-Ereignis   【Function()】", "【onDoubleTap】 : Doppelklick-Ereignis   【GestureTapCallback】", "【onLongPress】 : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>   【GestureLongPressCallback】"]}, {"file": "node2_tap.dart", "name": "Details von GestureDetector", "desc": ["【onTapDown】 : <PERSON><PERSON><PERSON><PERSON> beim <PERSON>   【GestureTapDownCallback】", "【onTapUp】 : <PERSON><PERSON><PERSON><PERSON> be<PERSON>   【GestureTapUpCallback】", "【onTapCancel】 : Klick abbrechen   【GestureTapCancelCallback】"]}, {"file": "node3_pan.dart", "name": "Pan-Ereign<PERSON> von GestureDetector", "desc": ["【onPanDown】 : <PERSON><PERSON><PERSON><PERSON> beim <PERSON>   【GestureDragDownCallback】", "【onPanEnd】 : <PERSON><PERSON> Ziehens   【GestureDragEndCallback】", "【onPanStart】 : Beginn des Ziehens   【GestureDragStartCallback】", "【onPanUpdate】 : Aktualisierung des Ziehens   【GestureDragUpdateCallback】", "【onPanCancel】 : <PERSON><PERSON><PERSON> abbrechen   【GestureDragCancelCallback】"]}]}