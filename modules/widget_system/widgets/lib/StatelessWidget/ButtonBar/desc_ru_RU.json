{"id": 29, "name": "<PERSON><PERSON><PERSON><PERSON>", "localName": "Панель кнопок", "info": "Принимает список компонентов, часто используется для размещения нескольких кнопок. Можно указать выравнивание, отступы и другую информацию.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Выравнивание ButtonBar", "desc": ["【alignment】: Выравнивание   【MainAxisAlignment】", "【children】: Набор дочерних компонентов   【List<Widget>】"]}, {"file": "node2_padding.dart", "name": "Отступы и высота ButtonBar", "desc": ["【buttonPadding】: Внутренние отступы   【EdgeInsetsGeometry】", "【buttonHeight】: Высота   【double】"]}]}