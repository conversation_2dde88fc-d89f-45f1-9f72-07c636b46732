{"id": 131, "name": "CupertinoActionSheet", "localName": "iOS лист действий", "info": "Всплывающая структура выбора в стиле iOS, которая может содержать несколько кнопок, обычно используется вместе с CupertinoActionSheetAction.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование CupertinoActionSheet", "desc": ["【title】 : Компонент первой строки   【Widget】", "【message】 : Компонент второй строки  【Widget】", "【cancelButton】 : Компонент кнопки отмены  【Widget】", "【actions】 : Список компонентов в середине  【List<Widget>】"]}]}