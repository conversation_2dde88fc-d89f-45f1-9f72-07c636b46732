{"id": 34, "name": "Divider", "localName": "Línea divisoria horizontal", "info": "Línea divisoria horizontal, se puede especificar el color, la altura, el grosor, la información de los márgenes izquierdo y derecho, comúnmente utilizada como línea divisoria de elementos de la lista.", "lever": 2, "family": 0, "linkIds": [35, 329], "nodes": [{"file": "node1_base.dart", "name": "Color y grosor de Divider", "desc": ["【color】: color   【Color】", "【thickness】: grosor de la línea   【double】"]}, {"file": "node2_height.dart", "name": "Altura y espacio de Divider", "desc": ["【indent】: longitud del espacio frontal   【double】", "【endIndent】: longitud del espacio posterior   【double】", "【height】: altura del espacio ocupado   【double】"]}]}