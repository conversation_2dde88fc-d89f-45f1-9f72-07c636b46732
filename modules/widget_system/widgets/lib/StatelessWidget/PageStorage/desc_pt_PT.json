{"id": 210, "name": "PageStorage", "localName": "Armazenamento de Página", "info": "Pode armazenar o estado da página, mantendo o estado ao alternar entre páginas. No código-fonte, é aplicado em ScrollView, PageView, ExpansionTile, etc.", "lever": 3, "family": 0, "linkIds": [52, 165], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do PageStorage", "desc": ["【bucket】: <PERSON><PERSON> de armazenamento 【PageStorageBucket】", "【child】: Compo<PERSON><PERSON> filho 【Widget】", "Ao alternar a interface e inicializar componentes, o estado não será redefinido. Como no CountWidget, o componente filho precisa ler o estado do armazenamento durante a inicialização e escrever o estado no armazenamento ao alterar o estado. Além disso, se usar o MaterialApp, o PageStorage já está embutido, mas você também pode criar um PageStorage."]}]}