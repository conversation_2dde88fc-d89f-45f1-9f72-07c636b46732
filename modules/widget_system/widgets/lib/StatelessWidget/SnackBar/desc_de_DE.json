{"id": 140, "name": "SnackBar", "localName": "Informationsleiste", "info": "Als Komponente handelt es sich um eine einfache Strukturkomponente, die Form, Schattentiefe, Hintergrundfarbe usw. angeben kann. Wird normalerweise über die showSnackBar-Methode von ScaffoldState von unten eingeblendet.", "lever": 4, "family": 1, "linkIds": [141, 142], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von SnackBar", "desc": ["【content】 : Mi<PERSON><PERSON> Inhalts-Widget   【Widget】", "【action】 : <PERSON><PERSON><PERSON> Schaltfläche   【SnackBarAction】", "【duration】 : Dauer   【Widget】", "【backgroundColor】 : Hintergrundfarbe   【Color】", "【elevation】 : Sc<PERSON>tentiefe   【double】", "【shape】 : Form   【ShapeBorder】", "【onVisible】 : Callback bei Anzeige  【Function()】"]}]}