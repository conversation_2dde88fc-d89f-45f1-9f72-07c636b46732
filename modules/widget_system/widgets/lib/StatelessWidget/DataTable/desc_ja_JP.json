{"id": 102, "name": "DataTable", "localName": "データテーブル", "info": "テーブルコンポーネントで、クリック、編集、ソートなどの操作を論理的に設定できます。", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "DataTableの基本使用", "desc": ["【columns】 : 列   【List<DataColumn>】", "【rows】 : 行  【List<DataRow>】"]}, {"file": "node2_operation.dart", "name": "DataTableのsort", "desc": ["【sortColumnIndex】 : 列番号   【int】", "【columnSpacing】 : 列間隔   【double】", "【sortAscending】 : 順序かどうか  【bool】"]}]}