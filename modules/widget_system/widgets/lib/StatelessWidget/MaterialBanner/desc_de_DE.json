{"id": 211, "name": "MaterialBanner", "localName": "Banner-Komponente", "info": "Materialstil Banner-Komponente, unterstützt links-mitte-rechts oder links-mitte-unten Struktur, kann <PERSON>, Hintergrundfarbe usw. angeben", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_one_btn.dart", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> von MaterialBanner in einer Zeile", "desc": ["【content】 : <PERSON><PERSON><PERSON>   【Widget】", "【leading】: <PERSON><PERSON>   【Widget】", "【actions】: Liste der rechten Komponenten   【List<Widget>】", "【padding】: Innenabstand   【EdgeInsetsGeometry】", "【forceActionsBelow】: Ob die Schaltflächen unten sind   【bool】", "【backgroundColor】: Hintergrundfarbe    【Color】"]}, {"file": "node2_two_btn.dart", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> von MaterialBanner in zwei Zeilen", "desc": ["【contentTextStyle】: Stil der mittleren Position   【TextStyle】", "【leadingPadding】: Rand der linken Komponente    【EdgeInsetsGeometry】", "Wenn die Anzahl der Endkomponenten größer als 1 ist, ist die Struktur der Komponente links-mitte-unten."]}]}