{"id": 14, "name": "InputChip", "localName": "Comprehensive Chip", "info": "Similar in style to the Chip component, it integrates click, delete, and select events into one. Note: Click event and select event cannot coexist.", "lever": 4, "family": 0, "linkIds": [11, 12, 13, 15, 153], "nodes": [{"file": "node1_base.dart", "name": "Can accept click and delete events", "desc": ["【onPressed】: Click event   【Function()】", "【onDeleted】: Delete event   【Function()】"]}, {"file": "node2_select.dart", "name": "Can accept select event", "desc": ["【selected】: Whether selected   【bool】", "【onSelected】: Select event   【Function(bool)】"]}]}