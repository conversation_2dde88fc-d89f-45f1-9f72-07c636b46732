{"id": 345, "name": "InheritedTheme", "localName": "継承テーマ", "info": "これは抽象クラスで、非常に多くの XXXTheme 関連のサブクラスがあり、色やテキストスタイルなどの属性を定義し、サブツリー内でこれらの属性を共有するために使用されます。", "lever": 1, "family": 5, "linkIds": [324, 326, 328, 329], "nodes": [{"file": "node1_base.dart", "name": "InheritedTheme 紹介", "desc": ["InheritedTheme.capture を使用して上位のテーマをキャプチャし、CapturedThemes オブジェクトを取得できます。このオブジェクトの wrap メソッドを使用して、キャプチャしたテーマをルート間で使用できます。", "    ", "", "class InheritedThemeDemo extends StatelessWidget {", "  const InheritedThemeDemo({Key? key) : super(key: key);", "", "", "  @override", "  Widget build(BuildContext context) {", "    return const DefaultTextStyle(", "      style: TextStyle(fontSize: 24, color: Colors.blue),", "      child: <PERSON><PERSON><PERSON>(),", "    );", "  ", "", "", "class TestBody extends StatelessWidget {", "  const TestBody({Key? key) : super(key: key);", "", "  @override", "  Widget build(BuildContext context) {", "", "    return GestureDetector(", "        onTap: () => _toNextPage(context),", "        child: Container(", "            height: 60,", "            margin: const EdgeInsets.only(left: 40,right: 40),", "            alignment: Alignment.center,", "            color: Theme.of(context).primaryColor.withOpacity(0.1),", "            child: const Text('InheritedTheme')));", "  ", "", "  void _toNextPage(BuildContext context) {", "     final NavigatorState navigator = Navigator.of(context);", "     final CapturedThemes themes =", "     InheritedTheme.capture(from: context, to: navigator.context);", "    ", "     Navigator.of(context).push(", "       MaterialPageRoute(", "         builder: (BuildContext _) {", "           return themes.wrap(Container(", "             alignment: Alignment.center,", "             color: Colors.white,", "             child: Text('Flutter Unit'),"]}]}