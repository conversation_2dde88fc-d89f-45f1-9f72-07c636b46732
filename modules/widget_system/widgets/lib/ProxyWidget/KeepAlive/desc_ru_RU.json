{"id": 316, "name": "KeepAlive", "localName": "Поддержание активности", "info": "Нужно ли сохранять состояние дочерних элементов в лениво загружаемом списке. Это базовая реализация AutomaticKeepAlive, обычно не используется отдельно.", "lever": 1, "family": 5, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Введение в KeepAlive", "desc": ["【child】 : *Дочерний компонент   【Widget】", "【keepAlive】 : *Сохранять ли активность   【bool】", "На уровне фреймворка Flutter используется только в AutomaticKeepAlive, в исходном коде также сказано, что он редко используется отдельно. Этот пример демонстрирует поддержание состояния элементов ListView."]}]}