{"id": 329, "name": "DividerTheme", "localName": "Estilo de la línea divisoria", "info": "Se utiliza principalmente para establecer propiedades predeterminadas unificadas para los componentes de tipo Divider descendientes, también se pueden obtener las propiedades predeterminadas de Divider a través de este componente.", "lever": 3, "family": 5, "linkIds": [34, 35], "nodes": [{"file": "node1_base.dart", "name": "Uso de DividerTheme", "desc": ["Los parámetros de atributos son similares a Divider, se pueden obtener los datos del tema de la línea divisoria a través de DividerTheme.of, \"", "también se pueden establecer estilos predeterminados para las líneas divisorias de los 【descendientes】 de DividerTheme, incluyendo color, grosor, altura, etc."]}]}