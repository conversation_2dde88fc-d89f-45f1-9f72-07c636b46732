{"id": 329, "name": "DividerTheme", "localName": "分割線スタイル", "info": "主に子孫のDividerタイプコンポーネントにデフォルトのプロパティを統一して設定するために使用されます。また、このコンポーネントを通じてデフォルトのDividerのプロパティを取得することもできます。", "lever": 3, "family": 5, "linkIds": [34, 35], "nodes": [{"file": "node1_base.dart", "name": "DividerThemeの使用", "desc": ["プロパティパラメータはDividerと類似しており、DividerTheme.ofを使用して分割線のテーマデータを取得できます。\"", "また、DividerTheme【子孫】の分割線にデフォルトのスタイルを設定することもできます。これには色、太さ、高さなどが含まれます。"]}]}