{"id": 109, "name": "Flexible", "localName": "Flexible Component", "info": "Can only be used in Row, Column, and Flex layouts. It can nest children to utilize the remaining space to extend the placeholder space, and the adaptation type can also be specified.", "lever": 3, "family": 5, "linkIds": [94, 106], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of Flexible", "desc": ["【child】: child   【Widget】", "【fit】: adaptation mode*2   【FlexFit】", "【flex】: remaining space allocation ratio   【int】"]}]}