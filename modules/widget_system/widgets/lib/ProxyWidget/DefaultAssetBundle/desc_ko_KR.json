{"id": 320, "name": "DefaultAssetBundle", "localName": "기본 리소스 번들", "info": "InheritedWidget으로, AssetBundle 객체를 설정한 후, 해당 노드 이후의 노드 컨텍스트에서 DefaultAssetBundle.of(context)를 통해 AssetBundle 객체를 얻어 리소스 파일에 접근할 수 있습니다.", "lever": 2, "family": 5, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "DefaultAssetBundle 소개", "desc": ["【bundle】 : *리소스 번들   【AssetBundle】", "【child】 : *자식 위젯  【Widget】", "우리는 후속 노드에서 사용할 수 있도록 자신만의 DefaultAssetBundle을 정의할 수도 있고, 기본값을 바로 사용할 수도 있습니다. 이 예제는 프레임워크에서 제공하는 DefaultAssetBundle을 통해 리소스 이미지를 로드하여 표시하는 방법을 보여줍니다."]}]}