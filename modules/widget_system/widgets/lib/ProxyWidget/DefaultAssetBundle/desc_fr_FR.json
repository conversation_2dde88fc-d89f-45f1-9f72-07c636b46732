{"id": 320, "name": "DefaultAssetBundle", "localName": "Paquet de ressources par défaut", "info": "Un InheritedWidget, après avoir défini l'objet AssetBundle, les nœuds suivants dans le contexte peuvent obtenir l'objet AssetBundle via DefaultAssetBundle.of(context) pour accéder aux fichiers de ressources.", "lever": 2, "family": 5, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Présentation de DefaultAssetBundle", "desc": ["【bundle】 : *Pa<PERSON> de ressources   【AssetBundle】", "【child】 : *Co<PERSON><PERSON>t enfant  【Widget】", "Nous pouvons définir notre propre DefaultAssetBundle pour une utilisation ultérieure par les nœuds suivants, ou utiliser directement celui par défaut. Ce cas montre comment charger et afficher une image de ressource en utilisant le DefaultAssetBundle fourni par le framework."]}]}