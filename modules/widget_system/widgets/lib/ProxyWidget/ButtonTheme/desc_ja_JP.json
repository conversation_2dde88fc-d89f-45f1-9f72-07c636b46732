{"id": 326, "name": "ButtonTheme", "localName": "ボタンスタイル", "info": "主に子孫のButtonタイプコンポーネントにデフォルトのプロパティを統一して設定するために使用されます。また、このコンポーネントを通じてデフォルトのButtonのプロパティを取得することもできます。", "lever": 3, "family": 5, "linkIds": [23, 25, 26, 27], "nodes": [{"file": "node1_base.dart", "name": "ButtonThemeの使用", "desc": ["MaterialButtonと同じ属性パラメータで、ButtonTheme.ofを使用してボタンのテーマデータを取得できます。", "また、ButtonThemeの【子孫】のボタンコンポーネントにデフォルトのスタイルを設定することもできます。これには色、形状、サイズなどが含まれます。"]}]}