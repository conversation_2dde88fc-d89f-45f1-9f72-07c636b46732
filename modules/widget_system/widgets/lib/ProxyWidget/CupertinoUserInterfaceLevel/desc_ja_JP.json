{"id": 337, "name": "CupertinoUserInterfaceLevel", "localName": "ユーザーインターフェースレベル", "info": "iOS の概念で、コンテンツの可視レベル UIUserInterfaceLevel で、base と elevated に分かれています。InheritedWidget として、主にこのデータを共有します。", "lever": 1, "family": 5, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "CupertinoUserInterfaceLevel 紹介", "desc": ["CupertinoUserInterfaceLevel.of(context) を使用して CupertinoUserInterfaceLevelData データを取得できます。また、このコンポーネントを使用してデータを設定し、サブツリーと共有することもできます。データの詳細については、以下を参照してください: https://developer.apple.com/documentation/uikit/uiuserinterfacelevel"]}]}