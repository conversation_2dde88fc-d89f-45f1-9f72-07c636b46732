{"id": 325, "name": "IconTheme", "localName": "Thème d'icône", "info": "Peut contenir un enfant, spécifie le style par défaut pour les icônes des descendants. Souvent utilisé pour unifier le style de plusieurs icônes identiques, évitant ainsi de les configurer une par une.", "lever": 3, "family": 5, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de IconTheme", "desc": ["Vous pouvez obtenir les données du thème d'icône via IconTheme.of, ou définir un style par défaut pour les composants d'icône des descendants de IconTheme, y compris la couleur, la transparence et la taille."]}]}