{"id": 319, "name": "Directionality", "localName": "Direzionalità", "info": "Imposta un valore di attributo uniforme per i componenti con l'attributo textDirection per i discendenti, oppure puoi ottenere l'attributo predefinito textDirection corrente tramite Directionality.of(context).", "lever": 2, "family": 5, "linkIds": [2, 94], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di Directionality", "desc": ["【textDirection】: direzione di disposizione del testo 【TextDirection】", "【child】: componente figlio 【Widget】"]}]}