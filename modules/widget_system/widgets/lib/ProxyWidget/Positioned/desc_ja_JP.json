{"id": 108, "name": "Positioned", "localName": "位置指定コンポーネント", "info": "Stack内でのみ使用可能で、上下左右の距離を指定してコンポーネントを正確に配置できます。", "lever": 3, "family": 5, "linkIds": [97, 159, 121], "nodes": [{"file": "node1_base.dart", "name": "Positionedの基本使用", "desc": ["【child】 : コンポーネント   【Widget】", "【top】 : 親の上端までの距離   【double】", "【right】 : 親の右端までの距離   【double】", "【left】 : 親の左端までの距離   【double】", "【bottom】 : 親の下端までの距離   【double】"]}]}