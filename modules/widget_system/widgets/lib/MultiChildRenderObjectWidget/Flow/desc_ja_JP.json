{"id": 99, "name": "Flow", "localName": "フローレイアウト", "info": "複数のコンポーネントを収容でき、自身で配置のプロキシを指定する必要があります。コンポーネントの配置を高度にカスタマイズでき、通常のレイアウトでは実現できない効果を実現します。レイアウトの王者、その名にふさわしい。", "lever": 5, "family": 3, "linkIds": [98, 94], "nodes": [{"file": "node_01.dart", "name": "Flow円形配置", "desc": ["【children】 : コンポーネントリスト   【List<Widget>】", "【delegate】 : プロキシ   【FlowDelegate】"]}, {"file": "node_02.dart", "name": "Flow円形とアニメーションの組み合わせ", "desc": ["アニメーションを使用して周囲のコンポーネントの位置を変更することで効果を実現"]}]}