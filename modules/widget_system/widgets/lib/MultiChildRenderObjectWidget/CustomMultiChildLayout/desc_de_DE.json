{"id": 341, "name": "CustomMultiChildLayout", "localName": "Allgemeines Multi-Kind-Layout", "info": "Verwenden Sie eine Proxy-Klasse, um das Layout einer Gruppe von Kinderkomponenten zu steuern. Die Kinderkomponenten müssen mit der LayoutId-Komponente gekennzeichnet werden.", "lever": 4, "family": 3, "linkIds": [315, 285], "nodes": [{"file": "node_01.dart", "name": "Grundlegende Verwendung von CustomMultiChildLayout", "desc": ["【children】 : Kinderkomponentensammlung   【List<Widget>】", "【delegate】 : Layout-Proxy   【MultiChildLayoutDelegate】"]}]}