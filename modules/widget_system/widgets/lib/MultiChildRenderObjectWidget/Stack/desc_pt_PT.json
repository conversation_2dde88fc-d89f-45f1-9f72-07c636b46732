{"id": 97, "name": "<PERSON><PERSON>", "localName": "Layout Em<PERSON><PERSON><PERSON><PERSON>", "info": "Pode acomodar vários componentes, dispondo os subcomponentes de forma empilhada, com o último ficando por cima. Possui a propriedade alignment, que pode ser usada em conjunto com o componente Positioned para um posicionamento preciso.", "lever": 5, "family": 3, "linkIds": [94, 95, 161], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do Stack", "desc": ["【children】 : Lista de componentes   【List<Widget>】", "【textDirection】 : Direção de disposição dos filhos   【MainAxisAlignment】", "【alignment】 : <PERSON><PERSON>   【AlignmentGeometry】", "【overflow】 : Modo de overflow   【Overflow】", "【fit】 : <PERSON><PERSON>   【StackFit】"]}, {"file": "node2_positioned.dart", "name": "Uso Combinado do Stack e Positioned", "desc": ["O componente Positioned só pode ser usado dentro de um Stack, permitindo especificar as distâncias de topo, esquerda, direita e inferior para posicionar um componente com precisão."]}]}