{"id": 343, "name": "ShrinkWrappingViewport", "localName": "Vista de Envolvimento de Redução", "info": "Associado à propriedade shrinkWrap do ScrollView. O ShrinkWrappingViewport ajusta o seu tamanho no eixo principal para se adaptar aos seus filhos, usado em casos sem restrições de limite.", "lever": 1, "family": 3, "linkIds": [349, 162, 163], "nodes": [{"file": "node1_base.dart", "name": "Introdução ao NestedScrollViewViewport", "desc": ["【offset】 : *deslocamento   【ViewportOffset】", "【axisDirection】 : direção do eixo   【AxisDirection】", "【crossAxisDirection】 : direção do eixo cruzado   【AxisDirection】", "【slivers】 : componentes filhos   【List<Widget>】", "【clipBehavior】 : comportamento de corte   【Clip】"]}]}