{"id": 110, "name": "Table", "localName": "테이블 컴포넌트", "info": "테이블을 표시하는 컴포넌트로, 테두리, 열 너비, 텍스트 방향 등의 속성을 지정할 수 있으며, 핵심 객체 유형은 TableRow입니다.", "lever": 4, "family": 6, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Table 기본 사용법", "desc": ["【children】 : 컴포넌트 리스트   【List<Widget>】", "【columnWidths】 : 열 너비   【Map<int, TableColumnWidth>】", "【defaultColumnWidth】 : 기본 열 너비  【TableColumnWidth】", "【border】 : 테두리   【TableBorder】", "【textDirection】 : 텍스트 방향   【TextDirection】", "【defaultVerticalAlignment】 : 셀 세로 정렬 모드   【TableCellVerticalAlignment】"]}]}