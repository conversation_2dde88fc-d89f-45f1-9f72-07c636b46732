{"id": 110, "name": "Table", "localName": "表格组件", "info": "用于展示表格的组件,可指定边线、列宽、文字方向等属性,核心对象类型是TableRow。", "lever": 4, "family": 6, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Table基本使用", "desc": ["【children】 : 组件列表   【List<Widget>】", "【columnWidths】 : 列宽   【Map<int, TableColumnWidth>】", "【defaultColumnWidth】 : 默认列宽  【TableColumnWidth】", "【border】 : 边线   【TableBorder】", "【textDirection】 : 文字方向   【TextDirection】", "【defaultVerticalAlignment】 : 单元格竖直方向对齐模式   【TableCellVerticalAlignment】"]}]}