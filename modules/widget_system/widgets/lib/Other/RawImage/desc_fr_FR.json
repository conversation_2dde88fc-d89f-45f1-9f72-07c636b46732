{"id": 313, "name": "RawImage", "localName": "Image originale", "info": "C'est le composant central pour implémenter le composant Image, capable d'afficher une image UI. Ses propriétés de base sont similaires à celles de Image, et il est rarement utilisé seul.", "lever": 2, "family": 6, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de RawImage", "desc": ["【image】 : Image   【ui.Image】", "【width】 : Largeur   【int】", "【height】: <PERSON>ur   【int】", "【isAntiAlias】: Anti-crénelage   【bool】", "【filterQuality】: Qualité de filtrage   【FilterQuality】", "De nombreuses propriétés sont similaires à Image, voir les détails."]}]}