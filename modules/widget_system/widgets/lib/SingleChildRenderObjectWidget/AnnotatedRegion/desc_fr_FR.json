{"id": 288, "name": "AnnotatedRegion", "localName": "Région annotée", "info": "Il y a un générique, dans le code source, il est uniquement utilisé dans app_bar, nav_bar pour modifier les variables d'état et le style de la barre de navigation, le générique est généralement SystemUiOverlayStyle.", "lever": 2, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "AnnotatedRegion modifie le style des variables d'état", "desc": ["【value】 : valeur   【T】", "【sized】 : fournit-il une taille   【bool】", "【child】 : composant enfant   【Widget】"]}]}