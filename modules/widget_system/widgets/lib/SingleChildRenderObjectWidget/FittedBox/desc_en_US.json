{"id": 87, "name": "FittedBox", "localName": "Fitted Box", "info": "Can accommodate a child component, using the fit property to determine the adaptation mode of the child component area relative to the parent component, and has the alignment property.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of FittedBox", "desc": ["【child】: Child component   【Widget】", "【fit】: Adaptation mode   【BoxFit】", "【alignment】: Alignment method   【AlignmentGeometry】"]}]}