{"id": 70, "name": "DecoratedBox", "localName": "Dekorationsbox", "info": "Kann ein Kindelement aufnehmen und es dekorieren. Die Kerneigenschaft ist decoration, mit der Rahmen, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Hintergrundbilder usw. festgelegt werden können.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von DecoratedBox", "desc": ["【decoration】 : Dekorationsobjekt   【Decoration】", "【position】 : <PERSON>ordergrundfarbe (links)/Hi<PERSON>grundfarbe (rechts)   【DecorationPosition】"]}, {"file": "node2_image.dart", "name": "Form und Bilddekoration von DecoratedBox", "desc": ["【shape】 : Form   【BoxShape】", "【image】 : Hintergrundbild   【DecorationImage】,"]}, {"file": "node3_border.dart", "name": "Rahmendekoration von DecoratedBox", "desc": ["【border】 : <PERSON><PERSON><PERSON>   【BoxBorder】,"]}, {"file": "node4_shape.dart", "name": "Formdekoration von DecoratedBox", "desc": ["Mit dem ShapeDecoration-Objekt kann die Rahmenform angegeben werden,"]}, {"file": "node5_line.dart", "name": "Unterstreichungsdekoration von DecoratedBox", "desc": ["Mit dem UnderlineTabIndicator-Objekt kann die Unterstreichung angegeben werden,"]}, {"file": "node6_flutterLogo.dart", "name": "FlutterLogoDecoration-Dekoration", "desc": ["Mit dem FlutterLogoDecoration-Objekt kann die Flutter-Icon-Dekoration angegeben werden (hat keine große Bedeutung),"]}]}