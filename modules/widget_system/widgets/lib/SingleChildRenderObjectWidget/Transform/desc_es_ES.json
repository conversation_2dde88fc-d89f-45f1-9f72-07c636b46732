{"id": 78, "name": "Transform", "localName": "Transformación", "info": "<PERSON><PERSON>e contener un subcomponente, y se puede transformar el subcomponente mediante una matriz de transformación 4*4.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_skew.dart", "name": "Transformación de sesgo skew", "desc": ["El sesgo x está controlado por el número R0C1, el parámetro de entrada es un valor en radianes, que representa el ángulo de sesgo.", "El sesgo y está controlado por el número R1C0, el parámetro de entrada es un valor en radianes, que representa el ángulo de sesgo."]}, {"file": "node2_translation.dart", "name": "Transformación de traslación translationValues", "desc": ["La traslación x está controlada por el número R0C3, el parámetro de entrada es un valor numérico, que representa la longitud de la traslación.", "La traslación y está controlada por el número R1C3, el parámetro de entrada es un valor numérico, que representa la longitud de la traslación.", "La traslación z está controlada por el número R2C3, el parámetro de entrada es un valor numérico, que representa la longitud de la traslación."]}, {"file": "node3_scale.dart", "name": "Transformación de escala diagonal3Values", "desc": ["La escala x está controlada por el número R0C0, el parámetro de entrada es un valor numérico, que representa la tasa de escala.", "La escala y está controlada por el número R1C2, el parámetro de entrada es un valor numérico, que representa la tasa de escala.", "La escala z está controlada por el número R2C2, el parámetro de entrada es un valor numérico, que representa la tasa de escala."]}, {"file": "node4_rotate.dart", "name": "Transformación de rotación rotation", "desc": ["La rotación x está controlada por R1C1, R1C2, R2C1, R2C2, el parámetro de entrada representa radianes.", "La rotación y está controlada por R0C0, R0C2, R2C0, R2C2, el parámetro de entrada representa radianes.", "La rotación z está controlada por R0C0, R0C1, R1C0, R1C1."]}, {"file": "node5_perspective.dart", "name": "Transformación de perspectiva rotation", "desc": ["La perspectiva está controlada por R3C1, R3C2, R3C3."]}]}