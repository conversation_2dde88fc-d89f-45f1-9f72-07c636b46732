{"id": 78, "name": "Transform", "localName": "变换", "info": "可容纳一个子组件，可以通过一个4*4的变换矩阵对子组件进行变换。", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_skew.dart", "name": "斜切变换skew", "desc": ["斜切x由R0C1数控制,入参为弧度值,表示斜切角度", "斜切y由R1C0数控制,入参为弧度值,表示斜切角度"]}, {"file": "node2_translation.dart", "name": "平移变换translationValues", "desc": ["平移x由R0C3数控制,入参为数值,表示平移长度", "平移y由R1C3数控制,入参为数值,表示平移长度", "平移z由R2C3数控制,入参为数值,表示平移长度"]}, {"file": "node3_scale.dart", "name": "缩放变换diagonal3Values", "desc": ["缩放x由R0C0数控制,入参为数值,表示缩放分率", "缩放y由R1C2数控制,入参为数值,表示缩放分率", "缩放z由R2C2数控制,入参为数值,表示缩放分率"]}, {"file": "node4_rotate.dart", "name": "旋转变换rotation", "desc": ["x旋转由R1C1、R1C2、R2C1、R2C2控制,入参表示弧度", "y旋转由R0C0、R0C2、R2C0、R2C2控制,入参表示弧度", "z旋转由R0C0、R0C1、R1C0、R1C1控制,"]}, {"file": "node5_perspective.dart", "name": "透视变换rotation", "desc": ["由R3C1、R3C2、R3C3控制透视"]}]}