{"id": 166, "name": "CustomPaint", "localName": "Drawing Component", "info": "Drawing through CustomPainter can achieve some complex custom drawing components, and it is the soul of custom components in Flutter.", "lever": 5, "family": 2, "linkIds": [], "nodes": [{"file": "node1_clock.dart", "name": "CustomPaint Line Drawing", "desc": ["【painter】: Painter   【CustomPainter】"]}, {"file": "node2_bezier.dart", "name": "Custom<PERSON><PERSON>t Bezier Curve", "desc": ["Flutter also supports complex drawings such as <PERSON><PERSON> curves."]}]}