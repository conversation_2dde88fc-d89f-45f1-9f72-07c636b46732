{"id": 81, "name": "UnconstrainedBox", "localName": "Caja sin restricciones", "info": "<PERSON><PERSON>e contener un componente hijo y elimina todas las restricciones de área de ese componente, mostrando su tamaño propio.", "lever": 3, "family": 1, "linkIds": [80], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de UnconstrainedBox", "desc": ["【child】 : <PERSON>mpo<PERSON><PERSON> hijo   【Widget】", "【constrainedAxis】 : Eje aún restringido*2   【Axis】", "【alignment】 : Modo de alineación   【AlignmentGeometry】"]}]}