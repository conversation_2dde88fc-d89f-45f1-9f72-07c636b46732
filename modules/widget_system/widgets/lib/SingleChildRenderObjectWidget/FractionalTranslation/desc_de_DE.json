{"id": 263, "name": "FractionalTranslation", "localName": "Teilverschiebung", "info": "Verschiebt das Kindelement durch die offset-Eigenschaft, die Verschiebung ist OffSet horizontal/vertikal * Größe des Kindelements.", "lever": 3, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von FractionalTranslation", "desc": ["【translation】 : Verschiebungsteilungswert   【Offset】", "【child】: Kindelement    【Widget】"]}]}