{"id": 75, "name": "Baseline", "localName": "Componente di base", "info": "Può contenere un componente figlio, controllando la posizione del componente figlio attraverso il controllo dell'altezza della linea di base. Generalmente utilizzato per i componenti di testo.", "lever": 2, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di Baseline", "desc": ["【child】 : Componente figlio   【Widget】", "【baseline】 : Posizione della linea di base   【double】", "【baselineType】 : Tipo di linea di base   【TextBaseline】"]}]}