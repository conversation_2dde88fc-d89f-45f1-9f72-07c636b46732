{"id": 201, "name": "AnimatedSize", "localName": "크기 애니메이션", "info": "자식 컴포넌트의 크기가 변경될 때 애니메이션을 통해 점진적으로 변화시킵니다. 지속 시간, 정렬 방식, 곡선, vsync 등의 속성을 지정할 수 있습니다.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "AnimatedSize 기본 사용", "desc": ["【child】 : 자식 컴포넌트   【Widget】", "【duration】 : 애니메이션 지속 시간   【Duration】", "【alignment】 : 정렬 방식   【AlignmentGeometry】", "【curve】 : 애니메이션 곡선   【Duration】", "【vsync】 : vsync   【TickerProvider】"]}]}