{"id": 72, "name": "RotatedBox", "localName": "Caja Rotada", "info": "<PERSON><PERSON><PERSON> contener un componente hijo, gir<PERSON><PERSON><PERSON> en sentido horario en quarterTurns*90°.", "lever": 2, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de RotatedBox", "desc": ["【child】 : <PERSON>mpo<PERSON><PERSON> hijo   【Widget】", "【quarterTurns】 : <PERSON><PERSON><PERSON><PERSON> 90° girar   【int】"]}]}