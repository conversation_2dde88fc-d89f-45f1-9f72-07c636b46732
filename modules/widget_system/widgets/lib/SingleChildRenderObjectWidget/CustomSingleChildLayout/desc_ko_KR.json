{"id": 285, "name": "CustomSingleChildLayout", "localName": "일반 단일 자식 레이아웃", "info": "하나의 자식 위젯을 수용하고, 대리 클래스를 지정하여 자식 위젯을 배치합니다. 대리 클래스는 부모 컨테이너 영역과 자식 위젯의 영역 크기 및 영역 제약 조건을 얻을 수 있습니다.", "lever": 3, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "CustomSingleChildLayout 기본 사용", "desc": ["【delegate】 : 대리   【SingleChildLayoutDelegate】"]}, {"file": "node2_offset.dart", "name": "CustomSingleChildLayout의 오프셋 사용", "desc": ["대리의 오프셋 기능을 활용하여 자식 위젯을 오프셋 위치시킬 수 있습니다."]}]}