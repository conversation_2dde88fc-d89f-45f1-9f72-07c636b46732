{"id": 266, "name": "CompositedTransformTarget", "localName": "Alvo de Transformação Composta", "info": "Geralmente usado em conjunto com o componente CompositedTransformFollower, pode fazer com que o Overlay acompanhe a transformação do alvo.", "lever": 3, "family": 2, "linkIds": [265, 182], "nodes": [{"file": "node1_base.dart", "name": "Uso do CompositedTransformTarget", "desc": ["【child】 : Compo<PERSON><PERSON> filho   【Widget】", "【link】 : Link   【LayerLink】"]}]}