{"id": 73, "name": "Opacity", "localName": "Transparenz", "info": "Kann ein Kindelement aufnehmen und dessen Transparenz in den Opazitätswert ändern, Opazität liegt zwischen 0 und 1.", "lever": 3, "family": 2, "linkIds": [89, 118], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von Opacity", "desc": ["【child】 : Kindelement   【Widget】", "【opacity】 : Transparenz 0~1   【double】"]}]}