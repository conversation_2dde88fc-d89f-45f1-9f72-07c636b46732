{"id": 292, "name": "IgnorePointer", "localName": "<PERSON><PERSON><PERSON>", "info": "Contiene un componente figlio e può decidere se il figlio ignora gli eventi del gesto specificando l'attributo ignoring, mentre esso stesso non accetta eventi.", "lever": 4, "family": 2, "linkIds": [295, 146, 149, 150], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di IgnorePointer", "desc": ["【child】 : Componente figlio   【Widget】", "【ignoring】 : Se ignorare gli eventi   【bool】", "Come mostrato di seguito, quando <PERSON>witch è selezionato, ignoring è true e l'evento del pulsante sarà bloccato, non cliccabile."]}]}