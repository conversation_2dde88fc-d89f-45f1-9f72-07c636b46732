{"id": 292, "name": "IgnorePointer", "localName": "Igno<PERSON>", "info": "Contains a child component, and it can decide whether the child ignores gesture events by specifying the ignoring property. It does not accept events itself.", "lever": 4, "family": 2, "linkIds": [295, 146, 149, 150], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of IgnorePointer", "desc": ["【child】 : Child component   【Widget】", "【ignoring】 : Whether to ignore events   【bool】", "As shown below, when the Switch is selected, ignoring is true, and the button event will be locked and cannot be clicked."]}]}