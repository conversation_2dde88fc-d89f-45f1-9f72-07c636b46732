{"id": 292, "name": "IgnorePointer", "localName": "Игнорировать клик", "info": "Содержит дочерний компонент, который может игнорировать жестовые события в зависимости от указанного свойства ignoring, сам по себе не принимает события.", "lever": 4, "family": 2, "linkIds": [295, 146, 149, 150], "nodes": [{"file": "node1_base.dart", "name": "Основное использование IgnorePointer", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【ignoring】 : Игнорировать ли события   【bool】", "Как показано ниже, когда Switch выбран, ignoring становится true, и события кнопки блокируются, делая её неактивной для кликов."]}]}