{"id": 83, "name": "OverflowBox", "localName": "Überlaufbox", "info": "Kann ein Kindelement aufnehmen, und das Kindelement darf den Bereich des Elternelements überlaufen. Es kann der maximale und minimale Bereich für Breite und Höhe festgelegt werden und verfügt über eine Ausrichtungseigenschaft alignment.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von OverflowBox", "desc": ["【child】 : Kindelement   【Widget】", "【minWidth】 : Minimale Breite   【double】", "【minHeight】 : Minimal<PERSON> Höhe   【double】", "【maxHeight】 : Maximale Höhe   【double】", "【maxWidth】 : Maximale Breite   【double】", "【alignment】 : Ausrichtung   【AlignmentGeometry】"]}]}