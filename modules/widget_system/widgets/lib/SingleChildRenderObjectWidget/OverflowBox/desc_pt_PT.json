{"id": 83, "name": "OverflowBox", "localName": "Caixa de Transbordamento", "info": "Pode conter um componente filho, e o componente filho pode transbordar a área do componente pai. Pode especificar as áreas mínimas e máximas de largura e altura para restringir, e possui o atributo de alinhamento alignment.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico do OverflowBox", "desc": ["【child】 : Compo<PERSON><PERSON> filho   【Widget】", "【minWidth】 : <PERSON><PERSON><PERSON> m<PERSON>   【double】", "【minHeight】 : <PERSON><PERSON> mínima   【double】", "【maxHeight】 : <PERSON><PERSON> máxima   【double】", "【maxWidth】 : <PERSON><PERSON><PERSON> máxima   【double】", "【alignment】 : <PERSON><PERSON>   【AlignmentGeometry】"]}]}