/// create by 张风捷特烈 on 2020/7/22
/// contact me <NAME_EMAIL>
/// 说明:

import 'package:flutter/material.dart';
import 'node2_save.dart';


void main() => runApp(const MyApp());

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
        title: 'Flutter Demo',
        theme: ThemeData(
          primarySwatch: Colors.blue,
        ),
        home: Scaffold(
          appBar: AppBar(),
          body: Center(child: RepaintBoundarySave()),
        ));
  }
}
