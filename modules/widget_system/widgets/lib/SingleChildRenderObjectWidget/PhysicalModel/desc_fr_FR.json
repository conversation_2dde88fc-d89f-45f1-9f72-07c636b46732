{"id": 296, "name": "PhysicalModel", "localName": "Module Physique", "info": "Permet de découper les composants enfants selon une forme circulaire ou carrée, et de spécifier la couleur de fond, les coins arrondis, la profondeur de l'ombre, la couleur de l'ombre et le comportement de découpe.", "lever": 3, "family": 2, "linkIds": [279, 69], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de PhysicalModel", "desc": ["【clipBehavior】 : Comportement de découpe   【Clip】", "【borderRadius】 : Coins arrondis   【BorderRadius】", "【child】 : <PERSON><PERSON><PERSON><PERSON> enfant   【Widget】", "【elevation】 : Profondeur de l'ombre   【double】", "【shadowColor】 : <PERSON><PERSON>ur de l'ombre   【Color】", "【shape】 : Forme   【BoxShape】", "【color】: Couleur    【Color】"]}]}