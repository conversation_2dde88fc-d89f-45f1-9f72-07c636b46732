{"id": 278, "name": "<PERSON>drop<PERSON><PERSON><PERSON>", "localName": "Filtro de fondo", "info": "<PERSON><PERSON><PERSON> contener un hijo y aplicar un filtro de desenfoque al fondo. El efecto de desenfoque en los componentes se puede lograr utilizando Stack para desenfocar el fondo.", "lever": 4, "family": 2, "linkIds": [88, 97, 67], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de BackdropFilter", "desc": ["【child】 : <PERSON>mpo<PERSON><PERSON> hijo   【Widget】", "【filter】 : Filtro   【ImageFilter】", "ImageFilter.blur puede implementar un desenfoque gaussiano, especificando los factores de desenfoque x e y."]}]}