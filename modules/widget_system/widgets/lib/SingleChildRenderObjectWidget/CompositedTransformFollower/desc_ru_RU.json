{"id": 265, "name": "CompositedTransformFollower", "localName": "Следователь композитного преобразования", "info": "Обычно используется совместно с компонентом CompositedTransformTarget, позволяя Overlay следовать за преобразованием цели.", "lever": 3, "family": 2, "linkIds": [266, 182], "nodes": [{"file": "node1_base.dart", "name": "Использование CompositedTransformFollower", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【link】 : Ссылка   【LayerLink】", "【offset】 : Смещение   【Offset】", "【targetAnchor】 : Якорь цели   【Alignment】", "【followerAnchor】 : Якорь следящего   【Alignment】", "【showWhenUnlinked】 : Показывать при отсутствии связи   【bool】"]}]}