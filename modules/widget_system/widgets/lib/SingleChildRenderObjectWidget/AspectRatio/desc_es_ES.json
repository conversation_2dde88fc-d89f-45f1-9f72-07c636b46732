{"id": 77, "name": "AspectRatio", "localName": "Caja de proporción", "info": "<PERSON><PERSON>e contener un componente hijo, limitando el área del componente hijo especificando la relación de aspecto aspectRatio.", "lever": 3, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de AspectRatio", "desc": ["【child】 : <PERSON>mpo<PERSON><PERSON> hijo   【Widget】", "【aspectRatio】 : Relación de aspecto   【double】"]}]}