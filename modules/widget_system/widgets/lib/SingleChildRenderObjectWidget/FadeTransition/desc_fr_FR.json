{"id": 89, "name": "FadeTransition", "localName": "Transition de transparence", "info": "Peut contenir un composant enfant et lui appliquer une animation de transition d'opacité, nécessite un animateur opacity.", "lever": 3, "family": 2, "linkIds": [73, 118], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de FadeTransition", "desc": ["【child】 : <PERSON><PERSON><PERSON><PERSON> enfant   【Widget】", "【opacity】 : Animation   【Animation<double>】"]}]}