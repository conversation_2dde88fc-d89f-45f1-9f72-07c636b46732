{"id": 357, "name": "ImageFiltered", "localName": "Filtro de Imagem", "info": "Com o filtro de imagem ImageFilter, você pode aplicar efeitos especiais a qualquer componente, incluindo, mas não se limitando a, desfoque gaussiano, filtros de cores, transformações, etc.", "lever": 4, "family": 2, "linkIds": [278, 88], "nodes": [{"file": "node1_blur.dart", "name": "Desfoque Gaussiano ImageFilter", "desc": ["【imageFilter】 : filtro de imagem   【ImageFilter】", "【child】 : component<PERSON> filho   【Widget】"]}, {"file": "node2_color.dart", "name": "Efeito de Filtro de Cor ImageFilter", "desc": ["Implementa o filtro de cor através do objeto ColorFilter."]}, {"file": "node3_matrix.dart", "name": "Efeito de Transformação ImageFilter", "desc": ["Realiza transformações de matriz através da construção ImageFilter.matrix, mas é um pouco limitado."]}]}