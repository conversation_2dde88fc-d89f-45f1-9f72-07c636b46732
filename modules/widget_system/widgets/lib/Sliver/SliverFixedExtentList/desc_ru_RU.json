{"id": 186, "name": "SliverFixedExtentList", "localName": "Sliver фиксированный расширяемый список", "info": "Компонент списка семейства Sliver, который создает дочерние компоненты через делегат и позволяет указать высоту элемента. Обычно используется в CustomScrollView.", "lever": 3, "family": 4, "linkIds": [183, 185, 187], "nodes": [{"file": "node1_base.dart", "name": "Основное использование SliverFixedExtentList", "desc": ["【itemExtent】 : Принудительная длина по главной оси   【double】", "【delegate】 : Де<PERSON><PERSON>г<PERSON><PERSON> дочерних элементов   【SliverChildDelegate】"]}]}