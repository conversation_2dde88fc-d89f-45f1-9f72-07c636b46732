{"id": 186, "name": "SliverFixedExtentList", "localName": "Sliver Fixed Extent List", "info": "A list component of the Sliver family, which constructs child components through a delegate and can specify the height of the item. It is commonly used in CustomScrollView.", "lever": 3, "family": 4, "linkIds": [183, 185, 187], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of SliverFixedExtentList", "desc": ["【itemExtent】: Forced length in the main axis   【double】", "【delegate】: Child delegate   【SliverChildDelegate】"]}]}