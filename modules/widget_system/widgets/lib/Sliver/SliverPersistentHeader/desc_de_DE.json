{"id": 190, "name": "SliverPersistentHeader", "localName": "<PERSON><PERSON><PERSON><PERSON>", "info": "Wird normal<PERSON><PERSON>se in CustomScrollView verwendet, um eine Komponente beim Scrollen oben zu fixieren, damit sie nicht verschwindet.", "lever": 5, "family": 4, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von SliverPersistentHeader", "desc": ["【delegate】 : Delegat   【SliverPersistentHeaderDelegate】", "【floating】 : <PERSON><PERSON> schwebend   【bool】", "【pinned】 : Ob oben fix<PERSON>t   【bool】"]}]}