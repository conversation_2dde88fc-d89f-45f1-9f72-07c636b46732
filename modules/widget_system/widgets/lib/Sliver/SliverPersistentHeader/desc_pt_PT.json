{"id": 190, "name": "SliverPersistentHeader", "localName": "Cabeçalho <PERSON>", "info": "Geralmente usado em CustomScrollView, permite que um componente permaneça no topo durante a rolagem, sem desaparecer.", "lever": 5, "family": 4, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do SliverPersistentHeader", "desc": ["【delegate】 : Delegado   【SliverPersistentHeaderDelegate】", "【floating】 : Se flutua   【bool】", "【pinned】 : Se fica no topo   【bool】"]}]}