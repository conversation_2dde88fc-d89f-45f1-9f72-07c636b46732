{"id": 187, "name": "SliverFillViewport", "localName": "Liste de vue de remplissage Sliver", "info": "Composant de liste de la famille Sliver, qui construit des sous-composants via un délégué, la hauteur de l'élément remplira la vue, et il est possible de spécifier si la fraction est divisée.", "lever": 3, "family": 4, "linkIds": [183, 185, 186], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de SliverFillViewport", "desc": ["【viewportFraction】 : Fraction de la vue   【double】", "【delegate】 : <PERSON><PERSON><PERSON><PERSON><PERSON> des enfants   【SliverChildDelegate】"]}]}