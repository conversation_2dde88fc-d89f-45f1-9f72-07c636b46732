{"id": 305, "name": "SliverIgnorePointer", "localName": "Sliver Ignorer les Événements", "info": "Peut envelopper un composant sliver, en contrôlant via 'ignoring' si ce composant sliver peut répondre aux événements.", "lever": 3, "family": 4, "linkIds": [183, 251], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de SliverIgnorePointer", "desc": ["【sliver】 : composant sliver   【Widget】", "【ignoring】 : ignorer les événements   【bool】"]}]}