{"id": 196, "name": "FlexibleSpaceBar", "localName": "확장 가능한 공간 바", "info": "일반적으로 SliverAppBar에서 확장 가능한 영역으로 사용되며, 제목, 제목 간격, 배경, 접기 모드 등을 지정할 수 있습니다.", "lever": 3, "family": 4, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "FlexibleSpaceBar 기본 사용법", "desc": ["【title】 : 제목 컴포넌트   【Widget】", "【titlePadding】 : 제목 간격   【EdgeInsetsGeometry】", "【collapseMode】 : 접기 모드   【CollapseMode】", "【stretchModes】 : 확장 모드   【List<StretchMode>】", "【background】 : 배경 컴포넌트   【Widget】", "【centerTitle】 : 중앙 정렬 여부   【bool】"]}]}