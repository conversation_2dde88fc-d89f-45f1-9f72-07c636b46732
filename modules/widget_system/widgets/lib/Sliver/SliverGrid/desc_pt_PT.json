{"id": 188, "name": "SliverGrid", "localName": "<PERSON><PERSON><PERSON>", "info": "Componente de lista de grelha da famí<PERSON>, semelhante ao Gird<PERSON>, construído atrav<PERSON> de count e extent. Geralmente usado em CustomScrollView.", "lever": 4, "family": 4, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de SliverList", "desc": ["SliverGrid.count especifica a construção do número axial", "SliverGrid.extent especifica a construção do comprimento axial", "As características dos atributos são semelhantes ao GridView, consulte para mais detalhes"]}]}