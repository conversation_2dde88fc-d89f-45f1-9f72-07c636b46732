{"id": 65, "name": "MaterialApp", "localName": "Material 앱", "info": "Material 앱의 최상위 컴포넌트로, 라우트 생성기, 테마, 언어, 홈페이지 등의 속성을 포함합니다.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "MaterialApp 기본 사용법", "desc": ["【theme】 : 테마   【ThemeData】", "【title】 : 작업 표시줄 제목   【String】", "【debugShowCheckedModeBanner】 : 배지 활성화   【bool】", "【showPerformanceOverlay】 : 성능 오버레이 활성화   【bool】", "【debugShowMaterialGrid】 : 그리드 활성화   【bool】", "【onGenerateRoute】 : 라우트 생성기   【RouteFactory】", "【home】 : 홈페이지   【Widget】"]}]}