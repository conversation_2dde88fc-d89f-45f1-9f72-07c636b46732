{"id": 65, "name": "MaterialApp", "localName": "Material应用", "info": "Material应用的顶级组件，包含路由生成器、主题、语言、主页等属性。", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "MaterialApp基本用法", "desc": ["【theme】 : 主题   【ThemeData】", "【title】 : 任务栏标题   【String】", "【debugShowCheckedModeBanner】 : 开启角标   【bool】", "【showPerformanceOverlay】 : 开启性能浮层   【bool】", "【debugShowMaterialGrid】 : 开启网格   【bool】", "【onGenerateRoute】 : 路由生成器   【RouteFactory】", "【home】 : 主页   【Widget】"]}]}