{"id": 112, "name": "SlideTransition", "localName": "Sc<PERSON>ebeübergang", "info": "Eine Unterklasse von AnimatedWidget, die einen Animator vom Typ Offset verwendet, um eine Übergangsanimation zwischen zwei Offset-Objekten für das Kind-Widget zu ermöglichen.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von SlideTransition", "desc": ["【child】 : Kind-Widget   【Widget】", "【textDirection】 : X-Achsenrichtung   【TextDirection】", "【position】 : Animation   【Animation<Offset>】"]}]}