{"id": 339, "name": "DateRangePickerDialog", "localName": "日付範囲", "info": "Materialスタイルの日付範囲ピッカーで、カレンダー選択と入力選択をサポートしています。", "lever": 4, "family": 1, "linkIds": [135, 136, 137], "nodes": [{"file": "node1_base.dart", "name": "DateRangePickerDialog 基本使用", "desc": ["【firstDate】 : 最も早い日付   【DateTime】", "【lastDate】 : 最も遅い日付   【DateTime】", "【initialDateRange】 : 初期範囲   【DateTimeRange?】", "【saveText】 : 保存テキスト  【String?】"]}, {"file": "node2_diy.dart", "name": "DateRangePickerDialog のカスタマイズ", "desc": ["DateRangePickerDialog のソースコードを変更して、月の項目に数値の背景を表示するようにします。"]}]}