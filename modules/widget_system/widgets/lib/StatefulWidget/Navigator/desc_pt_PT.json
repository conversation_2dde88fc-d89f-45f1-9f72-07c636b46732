{"id": 232, "name": "Navigator", "localName": "<PERSON><PERSON><PERSON><PERSON>", "info": "O Navigator gere um grupo de componentes filhos usando regras de pilha, podendo empurrar e retirar componentes filhos, bem como monitorar eventos de entrada e saída da pilha. A gestão de rotas do MaterialApp baseia-se no uso do Navigator.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do Navigator", "desc": ["【initialRoute】 : Rota inicial   【String】", "【onGenerateRoute】 : Gerador de rotas   【RouteFactory】", "【observers】 : Observador de rotas   【List<NavigatorObserver>】", "【onPopPage】 : Callback de saída da pilha   【PopPageCallback】"]}]}