{"id": 223, "name": "DropdownButtonFormField", "localName": "폼 드롭다운", "info": "기본적으로 DropdownButton에 의존하여 구현되었으므로 기본 속성이 유사합니다. 그러나 FormField의 특성을 가지고 있어 onSaved, validator 메서드를 콜백할 수 있습니다.", "lever": 2, "family": 1, "linkIds": [55, 222], "nodes": [{"file": "node1_base.dart", "name": "폼 드롭다운 간단 사용", "desc": ["【items】 : 하위 컴포넌트 리스트   【List<DropdownMenuItem<T>>】", "【validator】 : 폼 검증 콜백   【FormFieldValidator<T>】", "【onSaved】 : 폼 저장 콜백   【FormFieldSetter<T>】", "기타 속성은 DropdownButton을 참조하고, 폼 검증 특성은 FormField를 참조하세요."]}]}