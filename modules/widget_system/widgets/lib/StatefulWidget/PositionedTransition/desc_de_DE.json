{"id": 93, "name": "PositionedTransition", "localName": "Positionsübergang", "info": "Kann nur in einem Stack verwendet werden, kann ein Kindelement enthalten und lässt es eine Positionsanimation zwischen zwei Rechtecken durchführen, er<PERSON><PERSON> einen Animator rect.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von PositionedTransition", "desc": ["【child】 : Kindelement   【Widget】", "【rect】 : Animation   【Animation<RelativeRect>】", "    PositionedTransition-Komponente kann nur innerhalb eines Stacks funktionieren"]}]}