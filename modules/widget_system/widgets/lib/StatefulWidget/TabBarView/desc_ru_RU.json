{"id": 59, "name": "TabBarView", "localName": "Вкладки", "info": "Обычно используется вместе с TabBar для реализации эффекта скольжения страниц. Обычно не используется отдельно.", "lever": 2, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "TabBarView должен использоваться вместе с TabBar", "desc": ["【controller】 : Контро<PERSON>лер   【TabController】", "【children】 : Дети   【Цвет индикатора】", "【physics】 : Поведение   【ScrollPhysics】"]}]}