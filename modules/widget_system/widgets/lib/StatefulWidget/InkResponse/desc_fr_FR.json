{"id": 149, "name": "InkResponse", "localName": "Réponse d'ondulation", "info": "Effet de clic d'ondulation, reçoit les événements de clic, double-clic, appui long, annulation et changement de surbrillance. Peut spécifier la couleur de l'ondulation, le rayon, la forme de la surbrillance et d'autres propriétés.", "lever": 1, "family": 1, "linkIds": [150, 152], "nodes": [{"file": "node1_base.dart", "name": "Événements de base d'InkResponse", "desc": ["【child】 : <PERSON><PERSON><PERSON><PERSON> enfant   【Widget】", "【onTap】 : Événement de clic   【Function()】", "【onDoubleTap】 : Événement de double-clic   【Function()】", "【onTapCancel】 : Annulation de clic   【Function()】", "【onLongPress】 : Événement d'appui long   【Function()】"]}, {"file": "node2_color.dart", "name": "Autres propriétés d'InkResponse", "desc": ["【child】 : <PERSON><PERSON><PERSON><PERSON> enfant   【Widget】", "【onHighlightChanged】 : Rappel de changement de surbrillance   【Function(bool)】", "【highlightColor】 : <PERSON>uleur de surbrillance   【Color】", "【splashColor】 : <PERSON>uleur de l'ondulation   【Color】", "【radius】 : Rayon de l'ondulation   【double】"]}]}