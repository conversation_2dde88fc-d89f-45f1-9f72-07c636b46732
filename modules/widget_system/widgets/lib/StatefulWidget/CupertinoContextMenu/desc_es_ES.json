{"id": 143, "name": "CupertinoContextMenu", "localName": "Menú emergente de iOS", "info": "Un impresionante botón de estilo iOS que muestra un panel de menú emergente con animación cuando se mantiene presionado, generalmente se usa junto con CupertinoContextMenuAction.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de CupertinoContextMenu", "desc": ["【child】 : <PERSON>mpo<PERSON><PERSON> hijo   【Widget】", "【actions】 : Conjunto de componentes de acción   【List<Widget>】", "【previewBuilder】 : Constructor de animación  【ContextMenuPreviewBuilder】"]}]}