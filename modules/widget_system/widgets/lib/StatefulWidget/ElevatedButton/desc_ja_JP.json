{"id": 354, "name": "ElevatedButton", "localName": "昇格ボタン", "info": "Materialスタイルの昇格ボタンで、RaisedButtonと似たような動作をします。スタイルを変更して、ボーダー、色、影などのプロパティを変更できます。", "lever": 3, "family": 1, "linkIds": [353, 26, 355], "nodes": [{"file": "node1_base.dart", "name": "ElevatedButtonの基本使用", "desc": ["【child】 : スクロール主体を持つかどうか   【Widget】", "【onPressed】 : クリックイベント   【VoidCallback】", "【onLongPress】 : 長押しイベント   【VoidCallback】"]}, {"file": "node2_style.dart", "name": "ElevatedButtonのスタイル", "desc": ["【style】 : ボタンのスタイル   【ButtonStyle】", "【focusNode】 : フォーカス   【FocusNode】", "【clipBehavior】 : クリップ動作   【Clip】", "【autofocus】 : 自動フォーカス   【bool】"]}]}