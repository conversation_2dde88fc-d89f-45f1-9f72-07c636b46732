{"id": 54, "name": "TextField", "localName": "入力欄", "info": "入力用のコンポーネントで、複雑な属性を持っています。コントローラー、テキストスタイル、装飾線、行数制限、カーソルスタイルなどを指定できます。入力の変化、入力完了などのイベントを受け取ります。", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "TextFieldの基本使用法", "desc": ["【controller】 : コントローラー   【TextEditingController】", "【style】 : テキストスタイル   【TextStyle】", "【decoration】 : 装飾線   【InputDecoration】", "【onEditingComplete】 : 入力完了イベント   【Function()】", "【onSubmitted】 : 送信イベント   【Function(String)】", "【onChanged】 : 入力イベント   【Function(String)】"]}, {"file": "node2_cursor.dart", "name": "TextFieldの行数とカーソル", "desc": ["【minLines】 : 最小行数   【int】", "【maxLines】 : 最大行数   【int】", "【cursorRadius】 : カーソルの半径   【Radius】", "【cursorColor】 : カーソルの色   【Color】", "【cursorWidth】 : カーソルの幅   【double】", "【showCursor】 : カーソルを表示するかどうか   【bool】", "【autofocus】 : 自動フォーカス   【bool】"]}, {"file": "node3_decoration.dart", "name": "decorationの複雑な装飾", "desc": ["InputDecorationには非常に多くの装飾ポイントがあり、コードに対応する装飾があります:", "border: ボーダー関連", "helper: 左下のヒント関連", "counter: 右下のヒント関連", "prefix: 入力欄の内部最左側", "suffix: 入力欄の内部最右側", "label: フォーカスがない時のテキスト", "label: フォーカスがない時のテキスト", "hint: ヒントテキスト関連", "border: ボーダー関連"]}]}