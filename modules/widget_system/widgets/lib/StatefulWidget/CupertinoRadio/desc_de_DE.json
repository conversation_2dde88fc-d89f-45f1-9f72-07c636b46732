{"id": 240, "name": "CupertinoRadio", "localName": "Radio-Button im macOS-Stil", "info": "Ein Radio-Button im macOS-Stil, dessen Auswahlstatus durch value und groupValue bestimmt wird; onChanged ist der Callback für Klickereignisse.", "lever": 4, "family": 1, "linkIds": [19, 45], "nodes": [{"file": "node1.dart", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> von CupertinoRadio", "desc": ["【value】 : Wert des Radio-Buttons   【T】", "【groupValue】 : Aktueller übereinstimmender Wert   【T】", "【onChanged】 : Callback bei Änderung   【Function(T)】"]}, {"file": "node2.dart", "name": "<PERSON><PERSON> Cup<PERSON>inoRadio", "desc": ["【activeColor】 : Hintergrundfarbe im aktiven Zustand   【Color?】", "【fillColor】 : F<PERSON>llfarbe   【Color?】", "【inactiveColor】 : Hintergrundfarbe im inaktiven Zustand   【Color?】", "【focusColor】 : Fokusfarbe   【Color?】", "【mouseCursor】 : Mauszeigerstil   【MouseCursor?】"]}, {"file": "node3.dart", "name": "Umschaltbarkeit", "desc": ["【toggleable】 : Ist Umschaltbarkeit unterstützt   【bool】", "toggleable ist standardmäßig false. Wenn es true ist, wird bei einem Klick auf den aktiven Radio-Button ein null-Wert zurückgegeben, um die Anforderungen für die Auswahl und Nichtauswahl zu unterstützen."]}]}