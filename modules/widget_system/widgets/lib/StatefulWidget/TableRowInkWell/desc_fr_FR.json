{"id": 151, "name": "TableRowInkWell", "localName": "Ondu<PERSON> de Tableau", "info": "Utilisé uniquement pour les ondulations dans un Tableau, reçoit les événements de clic, double-clic, appui long et changement de surbrillance, l'ondulation s'applique à une ligne du tableau.", "lever": 1, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Événements de base de TableRowInkWell", "desc": ["【child】 : <PERSON><PERSON><PERSON><PERSON> enfant   【Widget】", "【onTap】 : Événement de clic   【Function()】", "【onDoubleTap】 : Événement de double-clic   【Function()】", "【onLongPress】 : Événement d'appui long   【Function()】", "【onHighlightChanged】 : Rappel de changement de surbrillance   【Function(bool)】"]}]}