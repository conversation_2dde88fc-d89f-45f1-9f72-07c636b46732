{"id": 116, "name": "AnimatedSwitcher", "localName": "<PERSON><PERSON>", "info": "Esegue un'animazione quando il componente figlio cambia, è necessario specificare la chiave del componente figlio per l'identificazione. Il metodo di animazione può essere personalizzato, è possibile specificare la durata dell'animazione, la curva dell'animazione e altre proprietà.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di AnimatedSwitcher", "desc": ["【child】 : Componente figlio   【Widget】", "【duration】 : Durata dell'animazione  【Duration】", "【switchOutCurve】 : Curva di uscita  【Curves】", "【switchInCurve】 : Curva di ingresso  【Curves】", "【switchInCurve】 : Curva di ingresso  【Curves】", "【transitionBuilder】 : Costruttore di animazione  【Widget Function(Widget, Animation<double>)】"]}]}