{"id": 57, "name": "AppBar", "localName": "Barra dell'applicazione", "info": "Una struttura comune per la barra superiore di un'applicazione, che consente di posizionare componenti specifici in aree designate, spesso utilizzata nel componente Scaffold.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di AppBar", "desc": ["【leading】 : Componente sinistro   【Widget】", "【title】 : Componente centrale   【Widget】", "【actions】 : Componente destro   【List<Widget>】", "【elevation】 : Profondità dell'ombra   【double】", "【shape】 : Forma   【ShapeBorder】", "【backgroundColor】 : Colore di sfondo   【Colore】", "【centerTitle】 : Centratura del titolo   【bool】"]}, {"file": "node2_tab.dart", "name": "Uso combinato di AppBar con TabBar e TabBarView", "desc": ["【bottom】 : Componente inferiore   【PreferredSizeWidget】"]}]}