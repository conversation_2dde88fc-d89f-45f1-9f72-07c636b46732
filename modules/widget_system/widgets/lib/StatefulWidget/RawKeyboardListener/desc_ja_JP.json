{"id": 254, "name": "RawKeyboardListener", "localName": "生キーボードリスナー", "info": "キーボードのキー押下とキーリリースのイベントを検出するために使用できます。現在は物理キーボードのみ検出可能で、デスクトップで使用できます。", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "RawGestureDetectorの基本使用", "desc": ["【onKey】 : キーボードイベント   【ValueChanged<RawKeyEvent>】", "【focusNode】 : フォーカス   【FocusNode】", "【autofocus】 : 自動フォーカス   【bool】", "【child】 : 子コンポーネント   【Widget】"]}]}