{"id": 144, "name": "CupertinoContextMenuAction", "localName": "iOS-Popup-Menü-Schaltfläche", "info": "Wird im Allgemeinen nur für die Klick-Schaltfläche in CupertinoContextMenu verwendet. Kann Kind- und Endsymbole angeben und empfängt Klick-Ereignisse.", "lever": 1, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von CupertinoContextMenuAction", "desc": ["【child】 : Untergeordnete Komponente   【Widget】", "【isDefaultAction】 : Standardmäßig ausgewählt  【bool】", "【trailingIcon】 : Ende  【bool】", "【onPressed】 : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>  【Function()】"]}]}