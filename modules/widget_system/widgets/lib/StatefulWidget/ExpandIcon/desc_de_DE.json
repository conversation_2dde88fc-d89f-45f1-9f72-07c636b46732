{"id": 51, "name": "ExpandIcon", "localName": "Erweitern-Symbol", "info": "Ein Erweitern-<PERSON><PERSON>, der beim Klicken eine 180-Grad-Drehanimation ausführt. Ka<PERSON>, Gr<PERSON><PERSON>, Rand angeben und Klickereignisse empfangen.", "lever": 1, "family": 1, "linkIds": [66, 125], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von ExpandIcon", "desc": ["【isExpanded】 : Ob erwei<PERSON>t   【bool】", "【padding】 : Innenabstand   【EdgeInsetsGeometry】", "【size】 : Symbolgröße   【double】", "【color】 : <PERSON>be wenn nicht erweitert   【Color】", "【expandedColor】 : <PERSON><PERSON> wenn erweitert   【Color】", "【onPressed】 : Klickereignis   【Function(bool)】"]}]}