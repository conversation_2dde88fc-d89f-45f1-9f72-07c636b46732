{"id": 139, "name": "CupertinoPicker", "localName": "Selettore iOS", "info": "Un selettore a scorrimento cilindrico di alta gamma, estremamente sofisticato, con molte proprietà configurabili e in grado di ricevere eventi di selezione durante lo scorrimento.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di CupertinoPicker", "desc": ["【children】 : Lista dei componenti figli   【List<Widget>】", "【offAxisFraction】 : Frazione di offset dell'asse   【double】", "【squeeze】 : Frazione di compressione   【double】", "【diameterRatio】 : Rapporto tra altezza e diametro del cilindro   【double】", "【itemExtent】 : Spaziatura   【double】", "【backgroundColor】 : Colore di sfondo   【Color】", "【onSelectedItemChanged】 : Evento di selezione  【Function(int)】"]}]}