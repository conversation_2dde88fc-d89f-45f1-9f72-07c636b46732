{"id": 178, "name": "ExpansionPanelList", "localName": "Раскрывающийся список", "info": "Раскрывающийся компонент списка, который может быть реализован для одиночного или множественного раскрытия в зависимости от логики. Можно указать продолжительность анимации раскрытия, принимает обратный вызов при раскрытии", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование ExpansionPanelList", "desc": ["【children】 : С<PERSON>ис<PERSON><PERSON> дочерних компонентов   【List<Widget>】", "【animationDuration】 : Продолжительность анимации   【Duration】", "【expansionCallback】 : Обратный вызов при раскрытии   【List<Widget>】", "【onPressed】 : Событие нажатия  【Function()】"]}]}