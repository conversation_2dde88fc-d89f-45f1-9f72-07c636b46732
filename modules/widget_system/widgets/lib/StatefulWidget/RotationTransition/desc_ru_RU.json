{"id": 90, "name": "RotationTransition", "localName": "Вращательный переход", "info": "Может содержать один дочерний компонент и анимировать его вращение. Требуется предоставить аниматор turns, имеет свойство alignment.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование RotationTransition", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【turns】 : Исчезнет ли   【Animation<double>】"]}]}