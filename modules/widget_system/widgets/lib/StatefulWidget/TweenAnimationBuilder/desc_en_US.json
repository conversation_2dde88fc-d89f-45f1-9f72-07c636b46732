{"id": 226, "name": "TweenAnimationBuilder", "localName": "Tween Animation Builder", "info": "Animates related properties through the Tween, and performs partial builds through the builder to reduce the refresh scope. No need for custom animators, you can specify animation duration, curve, and end callback.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "TweenAnimationBuilder Usage Example", "desc": ["【tween】 : *Tween   【Tween<T>】", "【duration】 : *Duration   【Duration】", "【builder】 : *Builder   【ValueWidgetBuilder<T>】", "【curve】 : Animation Curve   【Curve】", "【onEnd】 : End Callback   【VoidCallback】", "【child】 : Child Widget   【Widget】"]}]}