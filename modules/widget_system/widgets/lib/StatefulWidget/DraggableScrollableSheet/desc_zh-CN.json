{"id": 252, "name": "DraggableScrollableSheet", "localName": "拖滑页", "info": "可拖动和滑动的Sheet，可指定最大、最小、最初的分度现在滑动范围。构造器builder需要返回一个可滑动组件。", "lever": 2, "family": 1, "linkIds": [221, 142], "nodes": [{"file": "node1_base.dart", "name": "DraggableScrollableSheet基本使用", "desc": ["【initialChildSize】 : 初始分度   【double】", "【minChildSize】 : 最小分度   【double】", "【maxChildSize】 : 最大分度   【double】", "【builder】 : 滑动组件构造器   【ScrollableWidgetBuilder】", "【expand】 : 是否延展   【bool】"]}]}