{"id": 252, "name": "DraggableScrollableSheet", "localName": "드래그 가능한 시트", "info": "드래그 및 스크롤 가능한 시트로, 최대, 최소, 초기 분할 위치를 지정할 수 있습니다. 생성자 builder는 스크롤 가능한 컴포넌트를 반환해야 합니다.", "lever": 2, "family": 1, "linkIds": [221, 142], "nodes": [{"file": "node1_base.dart", "name": "DraggableScrollableSheet 기본 사용법", "desc": ["【initialChildSize】 : 초기 분할 위치   【double】", "【minChildSize】 : 최소 분할 위치   【double】", "【maxChildSize】 : 최대 분할 위치   【double】", "【builder】 : 스크롤 컴포넌트 생성자   【ScrollableWidgetBuilder】", "【expand】 : 확장 여부   【bool】"]}]}