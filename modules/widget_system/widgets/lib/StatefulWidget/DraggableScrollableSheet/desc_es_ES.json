{"id": 252, "name": "DraggableScrollableSheet", "localName": "<PERSON><PERSON>", "info": "Hoja que se puede arrastrar y deslizar, se puede especificar el tamaño máximo, mínimo e inicial dentro del rango de deslizamiento. El constructor builder debe devolver un componente deslizable.", "lever": 2, "family": 1, "linkIds": [221, 142], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de DraggableScrollableSheet", "desc": ["【initialChildSize】 : Tamaño inicial   【double】", "【minChildSize】 : <PERSON><PERSON><PERSON> mínimo   【double】", "【maxChildSize】 : <PERSON><PERSON><PERSON> máximo   【double】", "【builder】 : Constructor de componente deslizable   【ScrollableWidgetBuilder】", "【expand】 : Si se expande   【bool】"]}]}