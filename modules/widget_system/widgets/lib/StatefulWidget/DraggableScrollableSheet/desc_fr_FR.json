{"id": 252, "name": "DraggableScrollableSheet", "localName": "Feuille glis<PERSON> et déplaçable", "info": "<PERSON><PERSON>le déplaçable et glissable, permettant de spécifier les divisions maximales, minimales et initiales dans la plage de glissement. Le constructeur builder doit retourner un composant glissant.", "lever": 2, "family": 1, "linkIds": [221, 142], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de DraggableScrollableSheet", "desc": ["【initialChildSize】 : Division initiale   【double】", "【minChildSize】 : Division minimale   【double】", "【maxChildSize】 : Division maximale   【double】", "【builder】 : Constructeur de composant glissant   【ScrollableWidgetBuilder】", "【expand】 : S'étend ou non   【bool】"]}]}