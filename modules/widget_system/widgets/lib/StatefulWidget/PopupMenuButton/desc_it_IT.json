{"id": 56, "name": "PopupMenuButton", "localName": "Pulsante del menu", "info": "Apre un menu a comparsa, con la possibilità di specificare proprietà come offset, colore, profondità dell'ombra, forma, ecc. Riceve eventi di selezione e annullamento degli elementi.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di PopupMenuButton", "desc": ["【itemBuilder】 : Costruttore   【PopupMenuItemBuilder<T>】", "【offset】 : Offset   【Offset】", "【color】 : Colore di sfondo   【Color】", "【shape】 : Forma   【ShapeBorder】", "【elevation】 : Profondità dell'ombra   【double】", "【onCanceled】 : Evento di annullamento   【Function()】", "【onSelected】 : Evento di selezione   【Function(T)】"]}]}