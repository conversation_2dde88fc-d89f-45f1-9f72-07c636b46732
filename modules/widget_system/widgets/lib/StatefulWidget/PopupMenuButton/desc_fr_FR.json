{"id": 56, "name": "PopupMenuButton", "localName": "Bouton de menu", "info": "Affiche un menu contextuel, permettant de spécifier des propriétés telles que le décalage, la couleur, la profondeur d'ombre, la forme, etc. Reçoit les événements de sélection et de désélection des éléments.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de PopupMenuButton", "desc": ["【itemBuilder】 : Constructeur   【PopupMenuItemBuilder<T>】", "【offset】 : Décalage   【Offset】", "【color】 : Couleur de fond   【Color】", "【shape】 : Forme   【ShapeBorder】", "【elevation】 : Profondeur d'ombre   【double】", "【onCanceled】 : Événement d'annulation   【Function()】", "【onSelected】 : Événement de sélection   【Function(T)】"]}]}