{"id": 61, "name": "BottomAppBar", "localName": "Barre de navigation inférieure", "info": "Une barre de navigation inférieure encastrable, généralement utilisée au bas du composant Scaffold, peut spécifier des propriétés telles que la couleur, la profondeur de l'ombre, la forme, etc., et peut réaliser un effet de changement de page avec PageView.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de BottomAppBar", "desc": ["【elevation】 : Profondeur de l'ombre   【double】", "【shape】 : Forme   【NotchedShape】", "【notchMargin】 : Distance d'intervalle   【double】", "【color】 : Couleur   【Color】", "【child】 : Enfant   【Widget】"]}]}