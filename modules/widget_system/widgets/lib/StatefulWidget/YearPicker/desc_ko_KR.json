{"id": 136, "name": "YearPicker", "localName": "연도 선택기", "info": "연도를 선택하는 컴포넌트로, 외관이 다소 허술합니다. 선택 가능한 날짜 범위, 선택된 날짜 등을 지정할 수 있으며, 선택 이벤트를 수신합니다.", "lever": 3, "family": 1, "linkIds": [134, 135], "nodes": [{"file": "node1_base.dart", "name": "YearPicker 기본 사용법", "desc": ["【selectedDate】 : 선택된 날짜   【DateTime】", "【firstDate】 : 최초 날짜 제한   【DateTime】", "【lastDate】 : 최종 날짜 제한   【DateTime】", "【onChanged】 : 클릭 콜백  【Function(DateTime)】"]}]}