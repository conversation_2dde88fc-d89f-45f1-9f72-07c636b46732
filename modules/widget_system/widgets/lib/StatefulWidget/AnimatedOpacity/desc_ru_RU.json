{"id": 118, "name": "AnimatedOpacity", "localName": "Анимация прозрачности", "info": "Позволяет дочерним компонентам выполнять анимацию Opacity (прозрачности), можно указать продолжительность и кривую, есть событие завершения анимации.", "lever": 3, "family": 1, "linkIds": [89, 73], "nodes": [{"file": "node1_base.dart", "name": "Основное использование AnimatedOpacity", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【duration】 : Продолжительность анимации   【Duration】", "【onEnd】 : Обратный вызов завершения анимации   【Function()】", "【curve】 : Кривая анимации   【Duration】", "【opacity】 : Прозрачность   【double】"]}]}