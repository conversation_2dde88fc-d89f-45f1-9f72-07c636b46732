{"id": 118, "name": "AnimatedOpacity", "localName": "Transparenz-Animation", "info": "Ermöglicht die Animation der Opazität (Transparenz) von Kindkomponenten, ermöglicht die Angabe von Dauer und Kurve und verfügt über ein Ereignis zum Ende der Animation.", "lever": 3, "family": 1, "linkIds": [89, 73], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von AnimatedOpacity", "desc": ["【child】 : Kindkomponente   【Widget】", "【duration】 : Animationsdauer   【Duration】", "【onEnd】 : Rückruf am Ende der Animation   【Function()】", "【curve】 : Animationskurve   【Duration】", "【opacity】 : Transparenz   【double】"]}]}