{"id": 47, "name": "LinearProgressIndicator", "localName": "Горизонтальный прогресс", "info": "Линейный индикатор прогресса, можно указать цвет, прогресс и другие свойства. Если значение value равно null, индикатор будет вращаться бесконечно.", "lever": 3, "family": 1, "linkIds": [46, 48], "nodes": [{"file": "node1_base.dart", "name": "Базовое использование LinearProgressIndicator", "desc": ["【value】 : Прогресс   【double】", "【backgroundColor】 : Цвет фона   【Color】", "【valueColor】 : Цвет прогресса   【Animation<Color>】", "    Если значение value равно null, индикатор будет вращаться бесконечно"]}]}