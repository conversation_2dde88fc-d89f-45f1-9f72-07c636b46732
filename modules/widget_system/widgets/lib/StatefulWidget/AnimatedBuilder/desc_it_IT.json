{"id": 228, "name": "AnimatedBuilder", "localName": "Costruttore di animazioni", "info": "Attraverso il builder, il nodo corrispondente all'animazione viene aggiornato localmente, evitando l'aggiornamento dei componenti figli, riducendo il tempo di costruzione e migliorando le prestazioni dell'animazione.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Esempio di utilizzo di AnimatedBuilder", "desc": ["【animation】 : *oggetto ascoltabile   【Listenable】", "【builder】 : *costruttore di componenti   【TransitionBuilder】", "【child】 : componente figlio   【Widget】"]}]}