{"id": 137, "name": "CupertinoDatePicker", "localName": "Selettore di data iOS", "info": "Un selettore di data di alta qualità con scorrimento, che consente di specificare il tipo di selezione, l'intervallo di date, ecc., e riceve eventi di selezione della data.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di CupertinoDatePicker", "desc": ["【initialDateTime】 : Data iniziale   【DateTime】", "【minimumYear】 : Anno minimo   【int】", "【maximumYear】 : <PERSON><PERSON> massimo   【int】", "【onDateTimeChanged】 : Callback al click  【Function(DateTime)】", "【minuteInterval】 : Intervallo di minuti  【int】", "【use24hFormat】 : Se è in formato 24 ore  【bool】", "【backgroundColor】 : Colore di sfondo  【Color】", "【mode】 : Modalità*3  【CupertinoDatePickerMode】"]}]}