{"id": 113, "name": "DecoratedBoxTransition", "localName": "장식 변환", "info": "AnimatedWidget의 하위 클래스, Decorated 타입의 애니메이터를 사용하여 자식 위젯이 두 Decorated 객체 간에 전환 애니메이션을 수행합니다.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "DecoratedBoxTransition 기본 사용", "desc": ["【child】 : 자식 위젯   【Widget】", "【position】 : 전경/배경색  【DecorationPosition】", "【decoration】 : 애니메이션   【Animation<Decoration>】"]}]}