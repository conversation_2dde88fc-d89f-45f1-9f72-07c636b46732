{"id": 115, "name": "RelativePositionedTransition", "localName": "Transizione Posizione Rettangolo", "info": "Sottoclasse di AnimatedWidget, utilizza un animatore di tipo Rect per far eseguire un'animazione di transizione tra due oggetti Rect ai componenti figli.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso Base di RelativePositionedTransition", "desc": ["【child】 : Componente figlio   【Widget】", "【size】 : Offset sinistro e superiore   【Size】", "【rect】 : Animazione   【Animation<Rect>】", "    Il componente PositionedTransition funziona solo all'interno di uno Stack"]}]}