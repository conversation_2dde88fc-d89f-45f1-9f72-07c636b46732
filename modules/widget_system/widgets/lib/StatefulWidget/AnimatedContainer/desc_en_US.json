{"id": 123, "name": "AnimatedContainer", "localName": "Container Animation", "info": "Combines alignment, padding, color, decoration, width, height, constraints, margin, and transform, all of which can be animated. You can specify the duration and curve, and there is an animation end event.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of AnimatedContainer", "desc": ["【child】: Child component   【Widget】", "【duration】: Animation duration   【Duration】", "【onEnd】: Animation end callback   【Function()】", "【curve】: Animation curve   【Duration】", "【color】: Color   【Color】", "【width】: Width   【double】", "【height】: Height   【double】", "【alignment】: Alignment   【AlignmentGeometry】", "【decoration】: Decoration   【Decoration】", "【constraints】: Constraints   【BoxConstraints】", "【transform】: Transformation   【Matrix4】", "【margin】: Margin   【EdgeInsetsGeometry】", "【padding】: Padding   【EdgeInsetsGeometry】"]}]}