{"id": 123, "name": "AnimatedContainer", "localName": "Animation de conteneur", "info": "Combine alignment, padding, color, decoration, width, height, constraints, margin, transform en un seul, toutes ces propriétés peuvent être animées, la durée et la courbe peuvent être spécifiées, et il y a un événement de fin d'animation.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base d'AnimatedContainer", "desc": ["【child】 : <PERSON><PERSON><PERSON><PERSON> enfant   【Widget】", "【duration】 : Du<PERSON>e de l'animation   【Duration】", "【onEnd】 : Rappel de fin d'animation   【Function()】", "【curve】 : Courbe d'animation   【Duration】", "【color】 : Couleur   【Color】", "【width】 : Largeur   【double】", "【height】 : <PERSON><PERSON>   【double】", "【alignment】 : Alignement   【AlignmentGeometry】", "【decoration】 : Décoration   【Decoration】", "【constraints】 : Contraintes   【BoxConstraints】", "【transform】 : Transformation   【Matrix4】", "【margin】 : Marge extérieure   【EdgeInsetsGeometry】", "【padding】 : Marge intérieure   【EdgeInsetsGeometry】"]}]}