{"id": 123, "name": "AnimatedContainer", "localName": "애니메이션 컨테이너", "info": "alignment, padding, color, decoration, width, height, constraints, margin, transform을 모두 포함하며, 이러한 속성들은 모두 애니메이션 가능하며, 지속 시간과 곡선을 지정할 수 있고, 애니메이션 종료 이벤트가 있습니다.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "AnimatedContainer 기본 사용", "desc": ["【child】 : 자식 위젯   【Widget】", "【duration】 : 애니메이션 지속 시간   【Duration】", "【onEnd】 : 애니메이션 종료 콜백   【Function()】", "【curve】 : 애니메이션 곡선   【Duration】", "【color】 : 색상   【Color】", "【width】 : 너비   【double】", "【height】 : 높이   【double】", "【alignment】 : 정렬   【AlignmentGeometry】", "【decoration】 : 장식   【Decoration】", "【constraints】 : 제약   【BoxConstraints】", "【transform】 : 변환   【Matrix4】", "【margin】 : 외부 여백   【EdgeInsetsGeometry】", "【padding】 : 내부 여백   【EdgeInsetsGeometry】"]}]}