{"id": 224, "name": "AnimatedTheme", "localName": "アニメーションテーマ", "info": "テーマ変更時にアニメーション効果を持つコンポーネントで、本質的にはThemeコンポーネントとアニメーションを組み合わせたものです。ThemeData、アニメーション時間、曲線、終了コールバックなどを指定できます。強化版のThemeコンポーネントと言えます。", "lever": 2, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "AnimatedThemeの基本使用", "desc": ["【data】 : テーマデータ   【ThemeData】", "【duration】 : アニメーション時間   【Duration】", "【onEnd】 : アニメーション終了コールバック   【Function()】", "【curve】 : アニメーション曲線   【Duration】", "【child】 : 子コンポーネント   【Widget】"]}]}