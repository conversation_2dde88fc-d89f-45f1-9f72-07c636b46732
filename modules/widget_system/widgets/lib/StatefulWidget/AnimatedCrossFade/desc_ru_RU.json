{"id": 100, "name": "AnimatedCrossFade", "localName": "Переключение компонентов", "info": "При переключении двух компонентов отображается анимация, можно указать такие свойства, как кривая анимации, продолжительность, выравнивание и т.д. Это очень полезный компонент.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование AnimatedCrossFade", "desc": ["【firstChild】 : Первый ребенок   【Widget】", "【secondChild】 : Второй ребенок   【Widget】", "【crossFadeState】 : Показать какой   【CrossFadeState】", "【duration】 : Продолжительность   【Duration】"]}, {"file": "node2_curve.dart", "name": "Кривая анимации AnimatedCrossFade", "desc": ["【firstCurve】 : Первая кривая   【Curve】", "【secondCurve】 : Вторая кривая   【Curve】", "【sizeCurve】 : Кривая изменения размера   【CrossFadeState】"]}]}