{"id": 100, "name": "AnimatedCrossFade", "localName": "Transition de composants", "info": "Anime la transition entre deux composants avec des effets, en spécifiant des propriétés telles que la courbe d'animation, la durée, l'alignement, etc. C'est un composant très utile.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base d'AnimatedCrossFade", "desc": ["【firstChild】 : Premier enfant   【Widget】", "【secondChild】 : <PERSON>uxi<PERSON> enfant   【Widget】", "【crossFadeState】 : <PERSON><PERSON><PERSON><PERSON> le<PERSON>   【CrossFadeState】", "【duration】 : Du<PERSON>e   【Duration】"]}, {"file": "node2_curve.dart", "name": "Courbe d'animation d'AnimatedCrossFade", "desc": ["【firstCurve】 : Première courbe   【Curve】", "【secondCurve】 : Deuxième courbe   【Curve】", "【sizeCurve】 : Courbe de changement de taille   【CrossFadeState】"]}]}