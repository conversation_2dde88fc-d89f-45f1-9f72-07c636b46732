{"id": 41, "name": "CupertinoSwitch", "localName": "iOS-<PERSON><PERSON><PERSON>", "info": "Ein iOS-stilbasierter Umschalter, der häufig für die Konfigurationsumschaltung verwendet wird, kann Farben angeben und empfängt Rückrufe für Zustandsänderungen.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von CupertinoSwitch", "desc": ["【value】 : Ob ausgewählt   【double】", "【activeColor】 : Farbe im aktivierten Zustand   【Color】", "【onChanged】 : Umschalt-Rückruf   【Function(double)】"]}]}