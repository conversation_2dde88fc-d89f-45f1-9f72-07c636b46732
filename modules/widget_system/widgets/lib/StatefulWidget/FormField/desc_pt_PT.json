{"id": 222, "name": "FormField", "localName": "Campo de Formulário", "info": "Um campo de formulário, que precisa ser usado no componente Form, contém um campo do tipo genérico T como estado, e as atualizações e validações do campo acionam callbacks correspondentes.", "lever": 2, "family": 1, "linkIds": [198, 199, 223], "nodes": [{"file": "node1_base.dart", "name": "Introdução ao FormField", "desc": ["【builder】 : Con<PERSON><PERSON>tor de conteúdo   【FormFieldBuilder<T>】", "【initialValue】 : Valor inicial   【T】", "【validator】 : Função de validação   【FormFieldValidator<String> 】", "【enabled】 : Se está ativo   【bool】", "【onSaved】 : Callback ao salvar o formulário  【FormFieldSetter<String>】"]}]}