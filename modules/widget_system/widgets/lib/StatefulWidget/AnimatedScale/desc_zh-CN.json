{"id": 249, "name": "AnimatedScale", "localName": "缩放动画", "info": "给定的缩放量发生变化时，子组件可以自动调整相对于缩放值，且前后值有动画变化。", "lever": 3, "family": 1, "linkIds": [120, 247, 201], "nodes": [{"file": "node1.dart", "name": "缩放动画效果", "desc": ["该案例中，拖拽调节 scale 参数，查看动画运动效果。", "【child】 : 孩子组件   【Widget】", "【duration】 : 动画时长   【Duration】", "【onEnd】 : 动画结束回调   【Function()】", "【curve】 : 动画曲线   【Duration】", "【alignment】 : 动画变换中心   【Alignment】", "【filterQuality】 : 滤镜质量   【FilterQuality】", "【scale】 : 缩放量   【double】"]}]}