{"id": 249, "name": "AnimatedScale", "localName": "스케일 애니메이션", "info": "주어진 스케일 양이 변경될 때, 자식 구성 요소가 스케일 값에 따라 자동으로 조정되며, 이전 값과 이후 값 간에 애니메이션 변화가 있습니다.", "lever": 3, "family": 1, "linkIds": [120, 247, 201], "nodes": [{"file": "node1.dart", "name": "스케일 애니메이션 효과", "desc": ["이 예제에서는 scale 매개변수를 드래그하여 조정하고 애니메이션 효과를 확인합니다.", "【child】 : 자식 구성 요소   【Widget】", "【duration】 : 애니메이션 지속 시간   【Duration】", "【onEnd】 : 애니메이션 종료 콜백   【Function()】", "【curve】 : 애니메이션 곡선   【Duration】", "【alignment】 : 애니메이션 변환 중심   【Alignment】", "【filterQuality】 : 필터 품질   【FilterQuality】", "【scale】 : 스케일 양   【double】"]}]}