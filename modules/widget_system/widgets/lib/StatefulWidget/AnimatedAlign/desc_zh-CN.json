{"id": 120, "name": "AnimatedAlign", "localName": "对齐动画", "info": "能让子组件进行Align(对齐)动画，可指定时长和曲线，有动画结束事件。", "lever": 3, "family": 1, "linkIds": [85, 111], "nodes": [{"file": "node1_base.dart", "name": "AnimatedAlign基本使用", "desc": ["【child】 : 孩子组件   【Widget】", "【duration】 : 动画时长   【Duration】", "【onEnd】 : 动画结束回调   【Function()】", "【alignment】 : 对齐方式   【AlignmentGeometry】", "【curve】 : 动画曲线   【Duration】", "【padding】 : 内边距   【EdgeInsetsGeometry】"]}]}