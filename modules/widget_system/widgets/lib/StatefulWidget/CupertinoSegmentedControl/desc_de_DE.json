{"id": 262, "name": "CupertinoSegmentedControl", "localName": "iOS-Tab-Umschaltung", "info": "iOS-<PERSON><PERSON> mehr<PERSON>iste, <PERSON><PERSON><PERSON> wie ToggleButtons, kann Attribute wie Innenabstand, ausgewählte Farbe, Randfarbe usw. angeben.", "lever": 4, "family": 1, "linkIds": [33, 256], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von iOS-Tabs", "desc": ["【children】 : Widget-Map   【Map<T, Widget>】", "【onValueChanged】 : Rückru<PERSON> bei Wertänderung   【ValueChanged<T>】", "【groupValue】 : Ausgewählter Wert   【T】", "【padding】 : Innenabstand   【EdgeInsetsGeometry】"]}, {"file": "node2_color.dart", "name": "<PERSON><PERSON> von CupertinoSegmentedControl", "desc": ["【unselectedColor】 : Nicht ausgewählte Farbe   【Color】", "【selectedColor】 : Ausgewählte Farbe   【Color<T>】", "【pressedColor】 : Gedrückte Farbe   【Color】", "【borderColor】 : Randfarbe   【Color】"]}]}