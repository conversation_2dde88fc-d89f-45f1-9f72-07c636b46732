{"id": 231, "name": "InputDecorator", "localName": "Décorateur d'entrée", "info": "Un décorateur qui enveloppe l'entrée, est l'un des composants de base de TextField, généralement pas utilisé seul.", "lever": 2, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base d'InputDecorator", "desc": ["【decoration】 : Objet de décoration   【InputDecoration】", "【textAlign】 : Alignement du texte   【TextAlign】", "【child】 : <PERSON><PERSON><PERSON><PERSON> enfant   【Widget】"]}]}