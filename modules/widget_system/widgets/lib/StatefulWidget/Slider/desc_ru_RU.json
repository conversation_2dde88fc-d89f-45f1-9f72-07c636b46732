{"id": 42, "name": "Slide<PERSON>", "localName": "Ползунок", "info": "Компонент ползунка, который позволяет выбирать значение, перетаскивая между указанными минимальным и максимальным значениями. Можно указать цвет, количество сегментов и отображаемые метки, а также получить обратный вызов при изменении прогресса.", "lever": 4, "family": 1, "linkIds": [43, 44, 331], "nodes": [{"file": "node1_base.dart", "name": "Основное использование Slider", "desc": ["【value】 : значение   【double】", "【min】 : минимальное значение   【double】", "【max】 : максимальное значение   【double】", "【activeColor】 : активный цвет   【Color】", "【inactiveColor】 : неактивный цвет   【Color】", "【onChanged】 : обратный вызов при изменении   【Function(double)】"]}, {"file": "node2_lable.dart", "name": "Сегменты и метки Slider", "desc": ["【divisions】 : количество сегментов   【int】", "【label】 : текст подсказки   【String】", "【onChangeStart】 : слушатель начала перетаскивания   【Function(double)】", "【onChangeEnd】 : слушатель окончания перетаскивания   【Function(double)】"]}]}