{"id": 248, "name": "RawGestureDetector", "localName": "Rohgestenerkennung", "info": "<PERSON>nn verwendet werden, um die durch eine gegebene Gestenfabrik beschriebenen Gesten zu erkennen, was bei der Entwicklung eigener Gestenerkenner sehr nützlich ist. Für gängige Gesten verwenden Sie GestureRecognizer.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von RawGestureDetector", "desc": ["【behavior】 : Erkennungsverhalten   【HitTestBehavior】", "【gestures】 : Gestenzuordnung   【Map<Type, GestureRecognizerFactory>】", "【child】 : Untergeordnete Komponente   【Widget】"]}]}