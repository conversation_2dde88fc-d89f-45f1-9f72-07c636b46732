{"id": 104, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "localName": "拖拽目标", "info": "一个拖拽的目标区域，可接收Draggable组件的信息。可以获取拖拽时的回调。", "lever": 4, "family": 1, "linkIds": [103, 105], "nodes": [{"file": "node1_base.dart", "name": "DragTarget基本使用", "desc": ["【builder】 : 组件构造器   【DragTargetBuilder<T>】", "【onWillAccept】 : 拖入时   【Function(T)】", "【onAccept】 : 拖拽成功   【Function(T)】", "【onLeave】 : 拖入再脱出   【Function(T)】"]}]}