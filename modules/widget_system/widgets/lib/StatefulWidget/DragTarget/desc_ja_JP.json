{"id": 104, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "localName": "ドラッグターゲット", "info": "ドラッグ可能なコンポーネントの情報を受け取るためのドラッグターゲットエリアです。ドラッグ時のコールバックを取得できます。", "lever": 4, "family": 1, "linkIds": [103, 105], "nodes": [{"file": "node1_base.dart", "name": "DragTargetの基本使用", "desc": ["【builder】 : コンポーネントビルダー   【DragTargetBuilder<T>】", "【onWillAccept】 : ドラッグイン時   【Function(T)】", "【onAccept】 : ドラッグ成功時   【Function(T)】", "【onLeave】 : ドラッグイン後ドラッグアウト時   【Function(T)】"]}]}