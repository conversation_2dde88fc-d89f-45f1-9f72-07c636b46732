{"id": 176, "name": "Dismissible", "localName": "Скользящее исчезновение", "info": "При скольжении можно отобразить компонент внизу, можно указать направление скольжения и смещение по поперечной оси. Получает обратные вызовы при подтверждении исчезновения и исчезновении.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование Dismissible", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【background】 : Левый низ  【Widget】", "【secondaryBackground】 : Правый низ  【Widget】", "【key】 : Ключ  【Key】", "【confirmDismiss】 : Обратный вызов подтверждения  【DismissDirectionCallback】", "【onDismissed】 : Обратный вызов исчезновения  【DismissDirectionCallback】,"]}, {"file": "node2_direction.dart", "name": "Основное использование Dismissible", "desc": ["【direction】 : Направление   【DismissDirection】", "【crossAxisEndOffset】 : Смещение  【double】,"]}]}