{"id": 256, "name": "CupertinoSlidingSegmentedControl", "localName": "Pestañas deslizantes de iOS", "info": "Pestañas deslizantes al estilo de iOS, que admiten el cambio mediante clics y deslizamientos. Se pueden especificar propiedades como el color de la pestaña, el color de fondo, los márgenes, etc.", "lever": 3, "family": 1, "linkIds": [33, 262], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de pestañas deslizantes de iOS", "desc": ["【children】: Mapa de componentes 【Map<T, Widget>】", "【onValueChanged】: Callback de cambio de valor 【ValueChanged<T>】", "【groupValue】: <PERSON><PERSON> 【T】", "【thumbColor】: Color de selección 【Color】", "【backgroundColor】: Color de fondo 【Color】", "【padding】: <PERSON><PERSON><PERSON> interno 【EdgeInsetsGeometry】"]}]}