{"id": 199, "name": "TextFormField", "localName": "Textformularfeld", "info": "Die Eigenschaften sind im Wesentlichen dieselben wie bei TextField, zusätzlich gibt es Validierungs- und Rückruffunktionen für die Felder. Der FormState-save löst den onSaved-Rückruf aus.", "lever": 4, "family": 1, "linkIds": [54, 198], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von TextFormField", "desc": ["Die grundlegenden Eigenschaften sind identisch mit TextField, siehe dort für Details", "【validator】: Validierungsfunktion 【FormFieldValidator<String>】", "【onFieldSubmitted】: Einreichungsrückruf 【ValueChanged<String>】", "【onSaved】: <PERSON><PERSON><PERSON><PERSON> beim Speichern des Formulars 【FormFieldSetter<String>】"]}]}