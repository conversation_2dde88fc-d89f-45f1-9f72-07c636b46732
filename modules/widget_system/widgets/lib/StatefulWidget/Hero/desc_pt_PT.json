{"id": 171, "name": "Hero", "localName": "Animação Compartilhada", "info": "Pode especificar o nome da etiqueta, os componentes com a mesma etiqueta realizarão uma animação compartilhada ao saltar entre duas interfaces. Não pode haver duas etiquetas Hero com o mesmo nome numa interface.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do Hero", "desc": ["【tag】 : etiqueta   【String】,"]}]}