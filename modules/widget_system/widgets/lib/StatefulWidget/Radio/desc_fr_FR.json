{"id": 45, "name": "Radio", "localName": "Bouton de sélection", "info": "En raison des boutons ronds pour les états sélectionnés et non sélectionnés, plusieurs Radio peuvent répondre aux besoins de sélection unique ou multiple selon la logique. La couleur peut être spécifiée et un rappel de changement d'état peut être reçu.", "lever": 4, "family": 1, "linkIds": [19, 240], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de Radio", "desc": ["【value】 : <PERSON><PERSON> du bouton de sélection   【T】", "【groupValue】 : Valeur de correspondance actuelle   【T】", "【activeColor】 : Couleur active   【Color】", "【onChanged】 : <PERSON><PERSON> lors du changement   【Function(T)】"]}]}