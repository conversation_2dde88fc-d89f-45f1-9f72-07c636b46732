{"id": 45, "name": "Radio", "localName": "라디오 버튼", "info": "선택 및 미선택 상태의 원형 버튼으로, 여러 개의 라디오 버튼을 통해 논리에 따라 단일 선택 또는 다중 선택이 가능합니다. 색상을 지정할 수 있으며, 상태 변화 콜백을 받을 수 있습니다.", "lever": 4, "family": 1, "linkIds": [19, 240], "nodes": [{"file": "node1_base.dart", "name": "라디오 버튼 기본 사용법", "desc": ["【value】 : 라디오 버튼 값   【T】", "【groupValue】 : 현재 일치 값   【T】", "【activeColor】 : 활성화 색상   【Color】", "【onChanged】 : 변경 시 콜백   【Function(T)】"]}]}