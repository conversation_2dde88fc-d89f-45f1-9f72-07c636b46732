{"id": 182, "name": "Overlay", "localName": "Floating Component", "info": "Can display components in a floating manner across the entire application, allowing components to be added or removed, with an independently managed stack.", "lever": 5, "family": 1, "linkIds": [265, 266], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of Overlay", "desc": ["Overlay.of(context).insert inserts a global component"]}]}