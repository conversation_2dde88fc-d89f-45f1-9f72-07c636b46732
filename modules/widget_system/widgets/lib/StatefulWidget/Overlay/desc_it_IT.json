{"id": 182, "name": "Overlay", "localName": "Componente Sovrapposto", "info": "Consente di visualizzare i componenti in sovrapposizione su tutta l'applicazione, aggiungere o rimuovere componenti, che hanno una pila gestita in modo indipendente.", "lever": 5, "family": 1, "linkIds": [265, 266], "nodes": [{"file": "node1_base.dart", "name": "Uso Base di Overlay", "desc": ["Overlay.of(context).insert inserisce un componente globale"]}]}