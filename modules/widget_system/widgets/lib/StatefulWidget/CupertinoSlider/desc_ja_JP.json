{"id": 43, "name": "CupertinoSlider", "localName": "iOSスライダー", "info": "iOSスタイルのスライダーコンポーネントで、指定された最大値と最小値の間でドラッグして選択できます。色を指定でき、進捗変化のコールバックを受け取ります。", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "CupertinoSliderの基本使用", "desc": ["【value】 : 数値   【double】", "【min】 : 最小値   【double】", "【max】 : 最大値   【double】", "【activeColor】 : アクティブカラー   【Color】", "【thumbColor】 : サークルカラー   【Color】", "【divisions】 : セグメント数   【int】", "【onChangeStart】 : スライド開始コールバック   【Function(double)】", "【onChangeEnd】 : スライド終了コールバック   【Function(double)】", "【onChanged】 : 変更時コールバック   【Function(double)】"]}]}