{"id": 39, "name": "Checkbox", "localName": "Checkbox", "info": "Checkbox component, commonly used for toggling configurations, can specify colors, receive state change callbacks, and can also specify tristate.", "lever": 4, "family": 1, "linkIds": [326, 17], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of Checkbox", "desc": ["【value】 : Whether it is selected   【double】", "【checkColor】: Color of ✔️ when selected 【Color】", "【activeColor】: Color inside the box when selected 【Color】", "【onChanged】: State change event 【Function(bool)】,"]}, {"file": "node2_tristate.dart", "name": "Tristate of Checkbox", "desc": ["【tristate】 : Whether it is tristate   【double】", "  when onChanged,"]}]}