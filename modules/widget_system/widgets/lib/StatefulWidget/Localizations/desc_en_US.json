{"id": 290, "name": "Localizations", "localName": "Internationalization", "info": "Provides internationalization resources for subtree nodes, implemented with the underlying dependency on InheritedWidget. The MaterialApp internally integrates this component, and its locale and delegates parameters are ultimately used for this component.", "lever": 4, "family": 1, "linkIds": [65, 346], "nodes": [{"file": "node1.dart", "name": "Get Current Language", "desc": ["Through Localizations.localeOf(context), you can retrieve the Locale data information stored in the nearest upper-level Localizations component based on the context."]}]}