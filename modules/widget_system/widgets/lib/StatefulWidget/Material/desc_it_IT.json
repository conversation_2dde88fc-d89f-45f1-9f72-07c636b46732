{"id": 160, "name": "Material", "localName": "Componente Material", "info": "Leader dei componenti in stile Material, cuore e anima. Puoi specificare attributi come colore, profondità dell'ombra, tipo, colore dell'ombra, forma, ecc.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso base di Material", "desc": ["【child】 : Componente figlio   【Widget】", "【type】 : Tipo   【MaterialType】", "【elevation】 : Profondità dell'ombra   【double】", "【shadowColor】 : Colore dell'ombra   【Color】", "【color】 : Colore   【Color】"]}, {"file": "node2_shape.dart", "name": "Attributo shape di Material", "desc": ["【shape】 : Forma   【ShapeBorder】,"]}]}