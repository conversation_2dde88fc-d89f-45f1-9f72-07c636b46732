{"id": 253, "name": "Scrollable", "localName": "Composant défilable", "info": "Implémente un modèle d'interaction pour un composant défilable, nécessitant la construction d'une fenêtre d'affichage via viewportBuilder. C'est l'un des composants de base de ScrollView, généralement pas utilisé directement.", "lever": 4, "family": 1, "linkIds": [340, 349], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de Scrollable", "desc": ["【viewportBuilder】 : Constructeur de fenêtre d'affichage   【ViewportBuilder】", "【axisDirection】: Direction de défilement   【AxisDirection】", "【controller】: Contr<PERSON><PERSON>ur de défilement   【ScrollController】", "【dragStartBehavior】: Comportement de glissement   【DragStartBehavior】", "【physics】: Physique de défilement   【ScrollPhysics】,"]}]}