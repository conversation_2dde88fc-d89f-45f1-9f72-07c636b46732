{"id": 172, "name": "FutureBuilder", "localName": "<PERSON><PERSON><PERSON><PERSON>", "info": "Puede especificar un objeto Future, que puede monitorear el estado de la ejecución asíncrona y construir diferentes interfaces en el constructor según el estado. Tenga en cuenta que este objeto Future no se puede crear simultáneamente con FutureBuilder, de lo contrario podría provocar una actualización excesiva.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de FutureBuilder", "desc": ["【builder】 : Componente hijo   【AsyncWidgetBuilder<T>】", "【initialData】 : Datos iniciales   【T】", "【future】 : <PERSON><PERSON> asín<PERSON>  【Future<T>】"]}]}