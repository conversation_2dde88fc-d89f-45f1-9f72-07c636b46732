{"id": 282, "name": "Focus", "localName": "Компонент фокуса", "info": "Компонент, управляющий [FocusNode], который позволяет клавиатурному фокусу быть назначенным на этот виджет и его дочерние узлы.", "lever": 5, "family": 1, "linkIds": [283, 284], "nodes": [{"file": "node1.dart", "name": "Фокусировка и потер фокуса FocusNode", "desc": ["【focusNode】 : фокус   【FocusNode?】", "【onFocusChange】 : отслеживание изменений фокуса   【ValueChanged<bool>?】", "【child】 : дочерний компонент   【Widget】"]}, {"file": "node2.dart", "name": "Автоматическая фокусировка", "desc": ["【autofocus】 : автоматическая фокусировка   【bool】", "Фреймворк Flutter имеет встроенные сочетания клавиш для переключения фокуса, например, ← и → для горизонтального списка позволяют фокусироваться на предыдущем и следующем элементе. Клавиша Tab позволяет фокусироваться на следующем узле Focus."]}, {"file": "node3.dart", "name": "Обратный вызов событий клавиатуры", "desc": ["【onKeyEvent】 : обратный вызов событий клавиатуры   【FocusOnKeyEventCallback?】"]}]}