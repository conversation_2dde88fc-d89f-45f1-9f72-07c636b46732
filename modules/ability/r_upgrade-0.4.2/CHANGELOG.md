## 0.4.2
* fix issues(#76) support android 13.
## 0.4.1
* fix issues(#71)
## 0.4.0
* add silent install, fix google play release issues.
## 0.3.9
* fixed download file not exist error.
## 0.3.8+2
* fixed download file not exist error.
## 0.3.8+1
* fixed [installByPath] error.
## 0.3.8
* fixed get download header error,big file adapter,add [installByPath] method
## 0.3.7+3
* fixed Are you missing a call to unregister<PERSON>ec<PERSON>ver().
## 0.3.7+2
* fixed android 31 notification problem.
## 0.3.7+1
* Class [DownloadInfo] add [getSpeedString] method , change [LICENSE] to Apache License 2.0.
## 0.3.7
* fixed android compile SDK version = 31 crash error,upgrade version click cancel crash error、
## 0.3.6
* add android platform [androidStores]、[getVersionFromAndroidStore] methods.
## 0.3.5
* fix ios if not exist appId will crash error,add isChina params to [getVersionFrom]、[upgradeFromAppStore] method.
## 0.3.4
* fix 301/302 download error, larger then Android N network disconnect turn to connect download recovery.
## 0.3.3
* adapter null safety.
## 0.3.2+3
* adapter android Q.
## 0.3.2+2
* fix issues #23 again.
## 0.3.2+1
* fix issues #23.
## 0.3.2
* Repair the installation of two applications using the plug-in on the same device, resulting in two different but synchronous progress problems in the application, and the download failure occurred during the repair pause.
## 0.3.1+2
* Improve the download process and repair the problem that incremental update files do not exist.
## 0.3.1+1
* fix [#17](https://github.com/rhymelph/r_upgrade/issues/17)
## 0.3.1
* add `Android` permission check, when you use `upgrade` or `install`.
## 0.3.0
* remove `hotUpgrade` method,add increment upgrade.edit `upgrade` method.
## 0.2.6
* fix mini bug ,and change notification channel name ,fix example hot upgrade error.
## 0.2.5
* add `notificationStyle` params from `upgrade` method.
## 0.2.4
* add `upgradeFromAndroidStore` method.
## 0.2.2
* fix not use stream can not open notification,update `upgradeWithId` method.
## 0.2.1
* fix Android 6.0 install error.
## 0.2.0
* will power and awesome .
## 0.1.8
* adapter FlutterPlugin .
## 0.1.7
* add hot upgrade.
## 0.1.6
* fix file provider merge error and add can select auto apk install.
## 0.1.5
* add license.
## 0.1.4
* fix [issues/2](https://github.com/rhymelph/r_upgrade/issues/2#issue-524088878)
## 0.1.3
* fix progress will decrement.
## 0.1.2
* fix progress is null and speed is negative issus.
## 0.1.1
* add ios upgrade from AppStore.
## 0.1.0
* fix install apk.
## 0.0.2
* Improving health.
## 0.0.1
* release plugin.