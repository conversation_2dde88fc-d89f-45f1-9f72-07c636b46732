import 'package:flutter/material.dart';

/// create by 张风捷特烈 on 2020-04-19
/// contact me <NAME_EMAIL>
/// 说明:

class UnitColor {

  // 收藏夹提供的颜色
  static const collectColorSupport = <Color>[
    Color(0xFFF2F2F2),
    Colors.black,
    Colors.red,
    Colors.orange,
    Colors.yellow,
    Colors.green,
    Colors.blue,
    Colors.indigo,
    Colors.purple,

    Colors.cyanAccent,
    Color(0xffd1d08f),
    Colors.pink,
    Colors.amber,
    Colors.lime,
    Colors.teal,
    Colors.cyan,
    Color(0xff586CF2),
    Colors.purpleAccent,
  ];


  // 文字相关
  static const Color input_border_color = Color(0xffD0D7DD);
  static const Color text_color = Color(0xff323C47);
  static const Color input_hit_text_color = Color(0xff939EA7);
  static const Color head_text_color = Color(0xff666666);
  static const Color scaffoldBgLight = Color(0xffF3F4F6);

  // 缺省相关
  static const Color error_color = Colors.red;
  static const Color warning_color = Colors.orangeAccent;
}
