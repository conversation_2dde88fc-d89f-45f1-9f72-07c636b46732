import '../common/utils.dart';
import 'package:inteagle_monitoring_robot_app/src/bindings/bindings.dart';

class MeasurementRepository{

  Future<void> fetchMeasurements(String deviceId) async {

    String savePath = await getDatabasePath(deviceId);

    MeasurementQuery query = MeasurementQuery(
      databasePath: savePath,
      queryType: MeasurementQueryTypeDisplacement(),

    );
    query.sendSignalToRust();

  }



}
