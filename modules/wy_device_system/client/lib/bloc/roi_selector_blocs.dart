import 'dart:ui';
import '../model/target.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../repository/target_repository.dart';

abstract class TargetROISelectorEvent {
  const TargetROISelectorEvent();
}

class DrawModeChanged extends TargetROISelectorEvent {
  final bool isDrawMode;
  const DrawModeChanged(this.isDrawMode);
}

class ZoomLevelChanged extends TargetROISelectorEvent {
  final double level;
  const ZoomLevelChanged(this.level);
}

class ImageOffsetChanged extends TargetROISelectorEvent {
  final Offset offset;
  const ImageOffsetChanged(this.offset);
}

class TargetROISelectorState {
  final Target target;
  final bool isDrawMode;
  final double zoomLevel;
  final Offset imageOffset;
  const TargetROISelectorState(this.target, this.isDrawMode, this.zoomLevel,
      this.imageOffset);
}




class TargetROISelectorBloc extends Bloc<TargetROISelectorEvent, TargetROISelectorState> {



  TargetROISelectorBloc(super.initialState);


}
