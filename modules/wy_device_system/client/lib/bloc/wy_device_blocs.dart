import 'dart:ui';
import 'package:flutter/cupertino.dart';

import '../model/factory_calibration.dart';
import '../model/roi.dart';
import '../model/target.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../model/wy_device.dart';
import '../repository/target_repository.dart';
import '../model/target.dart';
import 'package:equatable/equatable.dart';
import 'package:path_provider/path_provider.dart';

import '../repository/wy_device_repository.dart';
import '../view/components/roi_selector/roi_scale_converter.dart';


abstract class WyDeviceEvent extends Equatable {
  const WyDeviceEvent();

  @override
  List<Object?> get props => [];
}

class ErrorOccurred extends WyDeviceEvent {
  final String message;

  const ErrorOccurred(this.message);

  @override
  List<Object?> get props => [message];
}

class WyDeviceBlocClearErrorMessage extends WyDeviceEvent {}


class LoadDeviceAttributes extends WyDeviceEvent {}

class SelectedCameraId extends WyDeviceEvent {
  final int selectedCameraId;

  SelectedCameraId(this.selectedCameraId);
  @override
  List<Object?> get props => [selectedCameraId];

}

class UpdateDeviceAttributes extends WyDeviceEvent {
  final WyDeviceAttribute deviceAttribute;

  const UpdateDeviceAttributes(this.deviceAttribute);

  @override
  List<Object?> get props => [deviceAttribute];
}

class UpdateSingleAttribute extends WyDeviceEvent {
  final String attributeName;
  final dynamic value;

  const UpdateSingleAttribute(this.attributeName, this.value);

  @override
  List<Object?> get props => [attributeName, value];
}

class ReceivedAttributeUpdate extends WyDeviceEvent {
  final WyDeviceAttribute attributes;
  const ReceivedAttributeUpdate(this.attributes);
  @override
  List<Object?> get props => [attributes];
}

class ReceivedWyDeviceStatusUpdate extends WyDeviceEvent {
  final String wyDeviceStatus;
  const ReceivedWyDeviceStatusUpdate(this.wyDeviceStatus);
  @override
  List<Object?> get props => [wyDeviceStatus];
}

class UpdateFactoryCalibration extends WyDeviceEvent {
  final FactoryCalibration params;
  const UpdateFactoryCalibration(this.params);

  @override
  List<Object?> get props => [];
}


class UpdateFactoryCalibrationMode extends WyDeviceEvent {
  final bool isFactoryCalibrationMode;

  const UpdateFactoryCalibrationMode(this.isFactoryCalibrationMode);

  @override
  List<Object?> get props => [isFactoryCalibrationMode];
}

class CalibrationRectChanged extends WyDeviceEvent {
  final Rect rect;

  const CalibrationRectChanged(this.rect);

  @override
  List<Object?> get props => [rect];
}

class CalibrationCameraChanged extends WyDeviceEvent {
  final int cameraId;

  const CalibrationCameraChanged(this.cameraId);

  @override
  List<Object?> get props => [cameraId];
}

class LoadHistoryImage extends WyDeviceEvent {
  const LoadHistoryImage();
  @override
  List<Object?> get props => [];
}

class HistoryImageLoaded extends WyDeviceEvent {

  final Map<int, Map<int, String>>  images ;
  const HistoryImageLoaded(this.images);

  @override
  List<Object?> get props => [images];
}

class DevicePictureUpdate extends WyDeviceEvent {
  final int cameraId;
  final String picturePath;
  const DevicePictureUpdate(this.cameraId, this.picturePath);

  @override
  List<Object?> get props => [cameraId,picturePath];
}

class StartFactoryCalibration extends WyDeviceEvent {

  @override
  List<Object?> get props => [];
}

class WyDeviceState extends Equatable {
  final WyDeviceAttribute? deviceAttribute;
  final WyDeviceStatus? wyDeviceStatus;
  final String deviceId;
  final String? selectedTargetId;
  final bool isLoading;
  final Map<int, String> cameraPicture;

  final String ipAddress;

  // 存储历史图像，格式: Map<时间戳, Map<相机ID, 图像路径>>
  final Map<int, Map<int, String>> historicalImages;
  // 当前选中的历史图像时间戳
  final int? selectedHistoricalTimestamp;

  final int selectedCameraId;
  final FactoryCalibration factoryCalibration;

  final bool isFactoryCalibrationMode;

  final Rect calibrationRect;

  final String? errorMessage; // 添加错误信息字段


  const WyDeviceState({
    this.deviceAttribute,
    required this.deviceId,
    this.cameraPicture = const {
      0: 'assets/images/sample_image.jpg',
    },
    required this.ipAddress,
    this.historicalImages = const {},
    this.selectedHistoricalTimestamp,
    this.wyDeviceStatus,
    this.selectedTargetId,
    this.isLoading = false,
    this.isFactoryCalibrationMode = false,
    this.calibrationRect = const Rect.fromLTWH(270, 190, 100, 100),
    this.selectedCameraId = 0,
    this.factoryCalibration = const FactoryCalibration(
      cameraId: 0,
      isCalibrated: false,
      ts: 0,
      targetModel: 'T1-DB3D300S7J',
      actualMeasureDistance: 1.0,
      roi: ROI(x:245, y:245, width: 100, height: 100, factorX: 1, factorY: 1),
    ),
    this.errorMessage,

  });

  WyDeviceState copyWith({
    WyDeviceAttribute? deviceAttribute,
    String? ipAddress,
    Map<int, String>? cameraPicture,
    Map<int, Map<int, String>>? historicalImages,
    int? selectedHistoricalTimestamp,
    String? deviceId,
    WyDeviceStatus? wyDeviceStatus,
    List<Target>? targets,
    String? selectedTargetId,
    bool? isLoading,
    bool? isFactoryCalibrationMode,
    Rect? calibrationRect,
    int? selectedCameraId,
    FactoryCalibration? factoryCalibration,
    String? errorMessage,


  }) {
    return WyDeviceState(
      deviceId: deviceId ?? this.deviceId,
      ipAddress: ipAddress ?? this.ipAddress,
      cameraPicture: cameraPicture?? this.cameraPicture,
      historicalImages: historicalImages ?? this.historicalImages,
      deviceAttribute: deviceAttribute ?? this.deviceAttribute,
      wyDeviceStatus: wyDeviceStatus ?? this.wyDeviceStatus,
      selectedTargetId: selectedTargetId ?? this.selectedTargetId,
      isLoading: isLoading ?? this.isLoading,
      isFactoryCalibrationMode: isFactoryCalibrationMode ?? this.isFactoryCalibrationMode,
      calibrationRect: calibrationRect ?? this.calibrationRect,
      selectedCameraId: selectedCameraId ?? this.selectedCameraId,
      factoryCalibration: factoryCalibration ?? this.factoryCalibration,
      errorMessage: errorMessage,
    );
  }

  @override
  List<Object?> get props => [
    deviceAttribute,
    ipAddress,
    cameraPicture,
    historicalImages,
    selectedHistoricalTimestamp,
    deviceId,
    wyDeviceStatus,
    selectedTargetId,
    isLoading,
    isFactoryCalibrationMode,
    calibrationRect,
    selectedCameraId,
    factoryCalibration,
    errorMessage,
  ];


  @override
  String toString() => 'WyDeviceState { '
      'deviceAttribute: $deviceAttribute, '
      'ipAddress: $ipAddress, '
      'cameraPicture: $cameraPicture, '
      'historicalImages: $historicalImages, '
      'selectedHistoricalTimestamp: $selectedHistoricalTimestamp, '
      'deviceId: $deviceId, '
      'wyDeviceStatus: $wyDeviceStatus, '
      'selectedTargetId: $selectedTargetId, '
      'isLoading: $isLoading },'
      'isFactoryCalibrationMode: $isFactoryCalibrationMode },'
      'selectedCameraId: $selectedCameraId },'
      'factoryCalibration: $factoryCalibration },'
      'errorMessage: $errorMessage },'
      'calibrationRect: $calibrationRect }';

}

class WyDeviceBloc extends Bloc<WyDeviceEvent, WyDeviceState> {
  final WyDeviceRepository repository = WyDeviceRepository();

  WyDeviceBloc({
    required String deviceId,
    ipAddress,
  }) : super(WyDeviceState(
      deviceId: deviceId,
      ipAddress: ipAddress,
  )) {
    on<LoadDeviceAttributes>(_onLoadDeviceAttributes);
    on<UpdateDeviceAttributes>(_onUpdateDeviceAttributes);
    on<UpdateSingleAttribute>(_onUpdateSingleAttribute);
    on<UpdateFactoryCalibrationMode>(_onUpdateFactoryCalibrationMode);
    on<CalibrationRectChanged>(_onUpdateFactoryCalibrationRectChange);
    on<ReceivedAttributeUpdate>(_onReceivedAttributeUpdate);
    on<ReceivedWyDeviceStatusUpdate>(_onReceivedWyDeviceStatusUpdate);
    on<SelectedCameraId>(_onSelectedCameraId);
    on<UpdateFactoryCalibration>(_onUpdateFactoryCalibration);
    on<CalibrationCameraChanged>(_onCalibrationCameraChanged);
    on<StartFactoryCalibration>(_onStartFactoryCalibration);
    on<ErrorOccurred>(_onErrorOccurred);
    on<WyDeviceBlocClearErrorMessage>(_onClearErrorMessage);
    on<DevicePictureUpdate>(_onDevicePictureUpdate);
    on<LoadHistoryImage>(_onLoadHistoryImage);

  }

  void _onLoadHistoryImage(
      LoadHistoryImage event,
      Emitter<WyDeviceState> emit) async {
    try {
      String cameraId = state.deviceId;
      final result = await repository.loadHistoryImage(cameraId);

      // print("LoadHistoryImage result: $result");
    } catch(e){
      print("LoadHistoryImage error: $e");
      emit(state.copyWith(isLoading: false));
      // 发出错误事件
      add(ErrorOccurred("加载历史图像失败: ${e.toString()}"));
    } finally {
      emit(state.copyWith(isLoading: false));
    }
  }

  void _onDevicePictureUpdate(
      DevicePictureUpdate event,
      Emitter<WyDeviceState> emit) {
    emit(state.copyWith(cameraPicture: {event.cameraId: event.picturePath}));
  }

  void _onClearErrorMessage(WyDeviceBlocClearErrorMessage event, Emitter<WyDeviceState> emit) {
    emit(state.copyWith(errorMessage: null));
  }

  void _onErrorOccurred(ErrorOccurred event, Emitter<WyDeviceState> emit) {
    emit(state.copyWith(errorMessage: event.message));
  }

  Future<void> _onStartFactoryCalibration(
      StartFactoryCalibration event,
      Emitter<WyDeviceState> emit) async {
    emit(state.copyWith(isLoading: true));

    var factoryCalibration = state.factoryCalibration;
    try {
      final result = await repository.startFactoryCalibration(
          factoryCalibration);
      debugPrint("Factory calibration started successfully: $result");
      emit(state.copyWith(isLoading: false));
    } catch (e) {
      debugPrint("Error starting factory calibration: $e");
      emit(state.copyWith(isLoading: false));
      // 发出错误事件
      add(ErrorOccurred("精度标定失败: ${e.toString()}"));
    }
  }

  void _onCalibrationCameraChanged(
      CalibrationCameraChanged event,
      Emitter<WyDeviceState> emit) {
    emit(state.copyWith(selectedCameraId: event.cameraId));
  }

  void _onUpdateFactoryCalibration(
      UpdateFactoryCalibration event,
      Emitter<WyDeviceState> emit) {
    emit(state.copyWith(factoryCalibration: event.params));
  }


  void _onSelectedCameraId(
      SelectedCameraId event,
      Emitter<WyDeviceState> emit) {
    emit(state.copyWith(selectedCameraId: event.selectedCameraId));
    final cameraFactoryCalibrations = state.deviceAttribute?.cameraFactoryCalibrations;
    bool foundCalibration = false;
    if(cameraFactoryCalibrations != null) {
      for(var element in cameraFactoryCalibrations) {
        if (element.cameraId == event.selectedCameraId) {
          emit(state.copyWith(factoryCalibration: element));
          foundCalibration = true;
          break;
        }
      }

      // 如果没有找到对应ID的标定数据，创建一个新的默认标定
      if(!foundCalibration) {
        var defaultCalibration = FactoryCalibration(
          cameraId: event.selectedCameraId,
          isCalibrated: false,
          ts: 0,
          focalLength: 25,
          targetModel: 'T1-DB3D300S7J',
          actualMeasureDistance: 20.0,
          roi: ROI(x:245, y:245, width: 100, height: 100, factorX: 1, factorY: 1),
        );
        emit(state.copyWith(factoryCalibration: defaultCalibration));
      }
    }
  }

  void _onReceivedWyDeviceStatusUpdate(
      ReceivedWyDeviceStatusUpdate event,
      Emitter<WyDeviceState> emit
      ) {

    if (state.deviceAttribute != null) {
      debugPrint('ReceivedWyDeviceStatusUpdate: ${event.wyDeviceStatus}');
      var newWyDeviceStatus = WyDeviceAttribute.parseDeviceStatus(event.wyDeviceStatus);
      debugPrint('state newWyDeviceStatus: $newWyDeviceStatus');

      emit(state.copyWith(wyDeviceStatus: newWyDeviceStatus));
    }
  }

  void _onReceivedAttributeUpdate(
      ReceivedAttributeUpdate event,
      Emitter<WyDeviceState> emit
      ) {
    // debugPrint('ReceivedAttributeUpdate: $event');
    //
    // if (state.deviceAttribute != null) {
    //   // 合并现有属性和新接收的属性，而不是完全替换
    //   debugPrint('state deviceAttribute: ${state.deviceAttribute!.toJson()}');
    //   final mergedAttributes = state.deviceAttribute!.copyWith(event.attributes);
    //   debugPrint('Merged attributes: ${mergedAttributes.toJson()}');
    //   emit(state.copyWith(deviceAttribute: mergedAttributes));
    // } else {
    //   // 如果之前没有属性，则直接使用新接收的属性
    //   // emit(state.copyWith(deviceAttribute: event.attributes));
    // }
  }

  void _onUpdateFactoryCalibrationRectChange(
      CalibrationRectChanged event,
      Emitter<WyDeviceState> emit
      ) {
    //更新出厂标定的ROI框
    ROI roi = ScaleConverter.scaleRectToOriginalROI(
        event.rect,
        Size(640, 480),ScaleConverter.defaultDeviceResolution
    );
    var factoryCalibration= state.factoryCalibration.copyWith(roi: roi);
    emit(state.copyWith(calibrationRect: event.rect, factoryCalibration: factoryCalibration));
  }

  void _onUpdateFactoryCalibrationMode(
      UpdateFactoryCalibrationMode event,
      Emitter<WyDeviceState> emit
      ) {
    debugPrint('UpdateFactoryCalibrationModeChange: ${event.isFactoryCalibrationMode}');

    final newState = state.copyWith(isFactoryCalibrationMode: event.isFactoryCalibrationMode);
    debugPrint('newState===>: $newState');
    emit(newState);
  }

  void _onLoadDeviceAttributes(
      LoadDeviceAttributes event,
      Emitter<WyDeviceState> emit
      ) async {
    emit(state.copyWith(isLoading: true));
    try {

      WyDeviceAttribute value = await repository.getDeviceAttributes(this.state.deviceId);
      emit(state.copyWith(
        deviceAttribute: value,
        isLoading: false,
      ));
    } catch (e) {
      emit(state.copyWith(isLoading: false));
    }
  }

  void _onUpdateDeviceAttributes(
      UpdateDeviceAttributes event,
      Emitter<WyDeviceState> emit
      ) async {
    emit(state.copyWith(isLoading: true));
    try {
      emit(state.copyWith(
        deviceAttribute: event.deviceAttribute,
        isLoading: false,
      ));
    } catch (e) {
      emit(state.copyWith(isLoading: false));
    }
  }

  void _onUpdateSingleAttribute(
      UpdateSingleAttribute event,
      Emitter<WyDeviceState> emit
      ) async{
    if (state.deviceAttribute == null) return;

    debugPrint('UpdateSingleAttribute: ${event.attributeName} - ${event.value}');
    final updatedAttribute = _updateAttributeValue(
        state.deviceAttribute!,
        event.attributeName,
        event.value
    );
    await repository.saveDeviceAttribute(event.attributeName, event.value);

    emit(state.copyWith(deviceAttribute: updatedAttribute));
  }

  WyDeviceAttribute _updateAttributeValue(
      WyDeviceAttribute attribute,
      String attributeName,
      dynamic value
      ) {
    switch (attributeName) {
      case 'model':
        return attribute.copyWith(model: value);
      case 'wyID':
        return attribute.copyWith(wyID: value);
      case 'ccid':
        return attribute.copyWith(ccid: value);
      case 'indicatorLight':
        return attribute.copyWith(indicatorLight: value);
      case 'current_fw_title':
        return attribute.copyWith(currentFwTitle: value);
      case 'current_fw_version':
        return attribute.copyWith(currentFwVersion: value);
      case 'linkedCaptureRule':
        return attribute.copyWith(linkedCaptureRule: value);
      case 'linkedCaptureType':
        return attribute.copyWith(linkedCaptureType: value);
      case 'linkedCaptureUrl':
        return attribute.copyWith(linkedCaptureUrl: value);
      case 'maximumAdaptivePwm':
        return attribute.copyWith(maximumAdaptivePwm: value);
      case 'minimumAdaptivePwm':
        return attribute.copyWith(minimumAdaptivePwm: value);
      case 'pwSelfAdaptive':
        return attribute.copyWith(pwSelfAdaptive: value);
      case 'reportEnvStatusInterval':
        return attribute.copyWith(reportEnvStatusInterval: value);
      case 'sigmaXDirection':
        return attribute.copyWith(sigmaXDirection: value);
      case 'sigmaYDirection':
        return attribute.copyWith(sigmaYDirection: value);
      case 'stableSample':
        return attribute.copyWith(stableSample: value);
      case 'uploadTailorData':
        return attribute.copyWith(uploadTailorData: value);
      case 'wyBrightnessThr':
        return attribute.copyWith(wyBrightnessThr: value);
      case 'wyCameraExposure':
        return attribute.copyWith(wyCameraExposure: value);
      case 'wyDeviceStatus':
        return attribute.copyWith(wyDeviceStatus: value);
      case 'wyEnableHeater':
        return attribute.copyWith(wyEnableHeater: value);
      case 'wyMaxSampleFreq':
        return attribute.copyWith(wyMaxSampleFreq: value);
      case 'wyMCUFirmwareV':
        return attribute.copyWith(wyMCUFirmwareV: value);
      case 'captureNum':
        return attribute.copyWith(captureNum: value);
      case 'wyHeaterTemperatureRange':
        return attribute.copyWith(wyHeaterTemperatureRange: value);
      case 'sim':
        return attribute.copyWith(sim: value);
      case 'filter':
        return attribute.copyWith(filter: value);
      case 'wyReportSelfAdaptiveInterval':
        return attribute.copyWith(wyReportSelfAdaptiveInterval: value);
      case 'pwm':
        return attribute.copyWith(pwm: value);
      case 'focalLength':
        return attribute.copyWith(focalLength: value);
      default:
        return attribute;
    }
  }
}
