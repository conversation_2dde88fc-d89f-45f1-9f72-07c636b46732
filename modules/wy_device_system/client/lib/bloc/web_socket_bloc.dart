import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../model/pixel_position.dart';
import '../model/target.dart';
import '../model/wy_device.dart';
import 'targets_blocs.dart';
import 'wy_device_blocs.dart';

// WebSocket事件
abstract class WebSocketEvent {}

class ConnectWebSocket extends WebSocketEvent {
  final String url;
  ConnectWebSocket(this.url);
}

class SendMessage extends WebSocketEvent {
  final Map<String, dynamic> message;
  SendMessage(this.message);
}

class MessageReceived extends WebSocketEvent {
  final Map<String, dynamic> message;
  MessageReceived(this.message);
}

class WebSocketErrorEvent extends WebSocketEvent {
  final String error;
  WebSocketErrorEvent(this.error);
}

class DisconnectWebSocket extends WebSocketEvent {}

class ReconnectWebSocket extends WebSocketEvent {}

// WebSocket状态
abstract class WebSocketState {}

class WebSocketInitial extends WebSocketState {}

class WebSocketConnecting extends WebSocketState {}

class WebSocketConnected extends WebSocketState {}

class WebSocketDisconnected extends WebSocketState {}

class WebSocketError extends WebSocketState {
  final String error;
  WebSocketError(this.error);
}

class WebSocketMessageReceived extends WebSocketState {
  final Map<String, dynamic> message;
  WebSocketMessageReceived(this.message);
}

class WebSocketBloc extends Bloc<WebSocketEvent, WebSocketState> {

  final TargetBloc? targetBloc;
  final WyDeviceBloc? deviceBloc;

  WebSocket? _webSocket;
  StreamSubscription? _webSocketSubscription;
  final String _url;
  final int _reconnectInterval;

  WebSocketBloc(
      String url, {
        this.targetBloc,
        this.deviceBloc,
        int reconnectInterval = 5,
      }) : _url = url,
        _reconnectInterval = reconnectInterval,
        super(WebSocketInitial()) {
    on<ConnectWebSocket>(_handleConnect);
    on<SendMessage>(_handleSendMessage);
    on<DisconnectWebSocket>(_handleDisconnect);
    on<MessageReceived>(_handleMessageReceived);
    on<WebSocketErrorEvent>(_handleWebSocketError);
    on<ReconnectWebSocket>(_handleReconnect);
  }

  Future<void> _handleConnect(
      ConnectWebSocket event,
      Emitter<WebSocketState> emit,
      ) async {
    try {
      emit(WebSocketConnecting());

      // 建立WebSocket连接
      _webSocket = await WebSocket.connect(event.url);

      // 监听WebSocket消息
      _webSocketSubscription = _webSocket?.listen(
            (data) {
          final message = jsonDecode(data);
          add(MessageReceived(message));
        },
        onError: (error) {
          add(WebSocketErrorEvent(error.toString()));
        },
        onDone: () {
          add(DisconnectWebSocket());
        },
      );
      emit(WebSocketConnected());
    } catch (e) {
      emit(WebSocketError(e.toString()));
      add(ReconnectWebSocket());
    }
  }

  void _handleMessageReceived(
      MessageReceived event,
      Emitter<WebSocketState> emit,
      ) {
    emit(WebSocketMessageReceived(event.message));
    _routeMessageToBlocs(event.message);

  }

  void _routeMessageToBlocs(Map<String, dynamic> message) {
    // 解析消息类型
    var isAttribute =  message.containsKey('Attributes');

    if (isAttribute) {
      // 处理设备属性消息
      var data = message['Attributes'] ?? {};
      // 判断是否只有一个属性wyDeviceStatus
      if (data.length == 1 && data.containsKey('wyDeviceStatus')) {
        deviceBloc?.add(ReceivedWyDeviceStatusUpdate(data['wyDeviceStatus']));
      } else {
        // 处理常规设备属性
        WyDeviceAttribute deviceAttribute = WyDeviceAttribute.fromJson(data);
        debugPrint('WebSocketBloc: Received device attribute: $deviceAttribute');
        deviceBloc?.add(ReceivedAttributeUpdate(deviceAttribute));
      }
      return;
    }

  }

  void _handleTargetUpdateMessage(Map<String, dynamic> message) {
    if (targetBloc != null && message.containsKey('target')) {
      final targetData = message['target'];
      if (targetData != null) {
        // 创建目标更新事件
        targetBloc!.add(TargetUpdated(
          Target(
            targetId: targetData['id'],
            name: targetData['name'] ?? 'Unknown',
            rect: targetData['rect'] != null
                ? Rect.fromLTWH(
              targetData['rect']['left'] ?? 0.0,
              targetData['rect']['top'] ?? 0.0,
              targetData['rect']['width'] ?? 50.0,
              targetData['rect']['height'] ?? 50.0,
            )
                : const Rect.fromLTWH(0, 0, 50, 50),
          ),
        ));
      }
    }
  }

  void _handleTargetInitializationResult(Map<String, dynamic> message) {
    if (targetBloc != null && message.containsKey('targetId')) {
      final targetId = message['targetId'];
      final bool success = message['success'] ?? false;

      // 获取原始的初始化状态映射
      final updatedInitializing = Map<String, bool>.from(
          targetBloc!.state.initializingTargets
      );
      // 移除正在初始化的标记
      updatedInitializing.remove(targetId);

      if (success) {
        // 初始化成功
        final target = targetBloc!.state.targets.firstWhere(
              (t) => t.targetId == targetId,
          orElse: () => Target(targetId: targetId, name: '', rect: Rect.zero),
        );

        // 创建更新后的标靶对象，包含参考点信息
        final updatedTarget = target.copyWith(
          refPoint: message['refPoint'] != null ?
          PixelPosition.fromJson(message['refPoint'] as Map<String, dynamic>) :
          PixelPosition(x: 0.0, y: 0.0, r: 0.0),
        );

        // 更新标靶
        targetBloc!.add(TargetUpdated(updatedTarget));

        // 清除任何存在的错误信息
        final updatedErrors = Map<String, String>.from(targetBloc!.state.initializationErrors);
        updatedErrors.remove(targetId);

        // 更新状态
        targetBloc!.emit(targetBloc!.state.copyWith(
            initializingTargets: updatedInitializing,
            initializationErrors: updatedErrors
        ));
      } else {
        // 初始化失败，添加错误处理事件
        final String errorMsg = message['error'] ?? '初始化失败';
        targetBloc!.add(TargetInitializeFailedEvent(targetId, errorMsg));
      }
    }
  }
  // 处理设备状态消息
  void _handleDeviceStatusMessage(Map<String, dynamic> message) {
    if (deviceBloc != null && message.containsKey('status')) {
      final status = message['status'];
      // deviceBloc!.add(DeviceStatusUpdated(status));
    }
  }

  void _handleWebSocketError(
      WebSocketErrorEvent event,
      Emitter<WebSocketState> emit,
      ) {
    emit(WebSocketError(event.error));
    add(ReconnectWebSocket());
  }

  Future<void> _handleSendMessage(
      SendMessage event,
      Emitter<WebSocketState> emit,
      ) async {
    if (_webSocket != null) {
      try {
        final message = jsonEncode(event.message);
        _webSocket!.add(message);
      } catch (e) {
        emit(WebSocketError(e.toString()));
        add(ReconnectWebSocket());
      }
    } else {
      emit(WebSocketError("WebSocket未连接，无法发送消息"));
      add(ReconnectWebSocket());
    }
  }

  Future<void> _handleDisconnect(
      DisconnectWebSocket event,
      Emitter<WebSocketState> emit,
      ) async {
    await _webSocketSubscription?.cancel();
    await _webSocket?.close();
    _webSocket = null;
    emit(WebSocketDisconnected());
    add(ReconnectWebSocket());
  }

  Future<void> _handleReconnect(
      ReconnectWebSocket event,
      Emitter<WebSocketState> emit,
      ) async {
    await Future.delayed(Duration(seconds: _reconnectInterval));
    add(ConnectWebSocket(_url));
  }



  @override
  Future<void> close() {
    _webSocketSubscription?.cancel();
    _webSocket?.close();
    return super.close();
  }
}
