import 'dart:async';
import 'dart:math';
import 'package:dio/dio.dart';
import 'package:utils/utils.dart';

import '../repository/telemetry_repository.dart';
import '../view/components/charts/model.dart';
import 'package:inteagle_monitoring_robot_app/src/bindings/bindings.dart';
class MeasurementDataService {
  final TelemetryRepository _telemetryRepository = TelemetryRepository();
  final StreamController<List<MeasurementData>> _dataStreamController = StreamController.broadcast();

  Stream<List<MeasurementData>> get dataStream => _dataStreamController.stream;

  // 用于缓存查询结果
  final Map<String, List<MeasurementData>> _dataCache = {};

  // WebSocket 订阅
  StreamSubscription? _wsSubscription;

  // 初始化服务
  Future<void> init(String deviceId) async {
    // 设置WebSocket数据监听
    _setupWebSocketListener(deviceId);
  }

  void _setupWebSocketListener(String deviceId) {
    _wsSubscription?.cancel();
    _wsSubscription = DisplacementRecord.rustSignalStream.listen((signalPack) {
      if (signalPack != null) {
        DisplacementRecord data = signalPack.message;
        List<DisplacementRecordModel> record = data.record;

        // 将接收到的实时数据转换为MeasurementData并发送到流
        for (var item in record) {
          final targetId = item.targetId;
          final dataPoint = MeasurementData(
              DateTime.fromMillisecondsSinceEpoch(item.ts.toInt()),
              sqrt(pow(item.sigmaX, 2) + pow(item.sigmaY, 2))
          );

          // 更新缓存
          _updateCache('${targetId}_displacement', dataPoint);

          // 发送更新通知
          _dataStreamController.add(_dataCache['${targetId}_displacement'] ?? []);
        }
      }
    });
  }

  void _updateCache(String key, MeasurementData newData) {
    if (!_dataCache.containsKey(key)) {
      _dataCache[key] = [];
    }

    _dataCache[key]!.add(newData);

    // 维护合理的缓存大小
    if (_dataCache[key]!.length > 1000) {
      _dataCache[key] = _dataCache[key]!.sublist(_dataCache[key]!.length - 1000);
    }
  }

  // 查询历史数据
  Future<List<MeasurementData>> queryHistoricalData({
    required String deviceId,
    required String targetId,
    required String dataType,
    required DateTime startTime,
    required DateTime endTime,
  }) async {
    final cacheKey = '${targetId}_${dataType}_${startTime.millisecondsSinceEpoch}_${endTime.millisecondsSinceEpoch}';

    // 检查缓存
    if (_dataCache.containsKey(cacheKey)) {
      return _dataCache[cacheKey]!;
    }

    // 从数据库查询
    final rawData = await _telemetryRepository.queryDisplacement(
      deviceId
    );

    // 转换数据
    final result = rawData.map((item) {
      return MeasurementData(
        DateTime.fromMillisecondsSinceEpoch(item['ts'] as int),
          item['sigma_x'] as double
      );
    }).toList();

    // 更新缓存
    _dataCache[cacheKey] = result;

    return result;
  }

  String _getDBFieldName(String dataType) {
    switch (dataType) {
      case 'sigmaX': return 'sigma_x';
      case 'sigmaY': return 'sigma_y';
      case 'temperature': return 'temperature';
      case 'humidity': return 'humidity';
      case 'pressure': return 'pressure';
      default: return dataType.toLowerCase();
    }
  }

  // 释放资源
  void dispose() {
    _wsSubscription?.cancel();
    _dataStreamController.close();
  }


  void startMeasureManager(Map<String, dynamic> rpc) async {


    var response = await HttpUtil.instance.client.post('/rpc', data: rpc);

    if (response.statusCode == 200) {
      // 处理成功的响应
      print('MeasureManager started successfully');
    } else {
      // 处理错误的响应
      print('Failed to start MeasureManager: ${response.statusCode}');
    }

  }
}
