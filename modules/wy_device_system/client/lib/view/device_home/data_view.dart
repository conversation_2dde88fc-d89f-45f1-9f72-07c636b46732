import 'dart:async';
import 'dart:math';
import 'package:client/view/components/charts/flexible-line-chart.dart';
import 'package:graphic/graphic.dart';
import 'package:client/model/measurement_query.dart';
import 'package:client/view/device_home/device_status_view_model.dart';
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:graphic/graphic.dart';
import 'package:intl/intl.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class Field {
  final MaterialColor color;
  final String key;
  final String label;
  bool visibility;

  Field(
      {required this.color,
      required this.key,
      required this.label,
      required this.visibility});
}

class DataView extends StatefulWidget {
  final List<Map<String, dynamic>> data;
  final bool isShowTable;
  const DataView({super.key, required this.data, required this.isShowTable});

  @override
  State<DataView> createState() => _DataViewState();
}

class _DataViewState extends State<DataView> {
  /// 自动刷新定时器
  Timer? _refreshTimer;

  List<Field> yFields = [
    Field(color: Colors.blue, key: 'sigmaX', label: 'X', visibility: true),
    Field(color: Colors.red, key: 'sigmaY', label: 'Y', visibility: true),
  ];
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  List<dynamic> get _chartData {
    if (widget.data.isEmpty) return [];
    return widget.data;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isShowTable) {
      final tableData = widget.data.map((point) {
        return {
          'timestamp':
              DateFormat('yyyy-MM-dd HH:mm:ss').format(point['timestamp']),
          'sigmaX': point['sigmaX'].toString(),
          'sigmaY': point['sigmaY'].toString(),
        };
      }).toList();

      return TDTable(
        height: 300,
        columns: [
          TDTableCol(title: '时间', colKey: 'timestamp'),
          TDTableCol(title: '横向位移', colKey: 'sigmaX'),
          TDTableCol(title: '竖向位移', colKey: 'sigmaY'),
        ],
        data: tableData,
      );
    } else {
      return Column(
        children: [
          _buildLegend(),
          Expanded(
            child: widget.data.length > 10000
                ?
                // 大数据集优化
                RepaintBoundary(
                    child: _buildChart(),
                  )
                :
                // 小数据集普通渲染
                _buildChart(),
          ),
        ],
      );
    }
  }

  Widget _buildLegend() {
    return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        spacing: 12,
        children: yFields.map((item) {
          return InkWell(
            onTap: () {
              setState(() {
                item.visibility = !item.visibility;
              });
            },
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
              child: Row(
                spacing: 6,
                children: [
                  Container(
                    width: 24,
                    height: 4,
                    decoration: BoxDecoration(
                      color: item.visibility
                          ? item.color
                          : item.color.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  Container(
                    width: 10,
                    height: 10,
                    decoration: BoxDecoration(
                      color: item.visibility
                          ? item.color
                          : item.color.withOpacity(0.3),
                      shape: BoxShape.circle,
                    ),
                  ),
                  Text(
                    item.label,
                    style: TextStyle(
                      color: item.visibility ? Colors.black : Colors.black38,
                      fontWeight:
                          item.visibility ? FontWeight.bold : FontWeight.normal,
                      decoration:
                          item.visibility ? null : TextDecoration.lineThrough,
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList());
  }

  Widget _buildChart() {
    if (widget.data.isEmpty) {
      return const Center(child: Text('暂无数据'));
    }
    // 准备可见字段
    final visibleFields = yFields.where((field) => field.visibility).toList();
    if (visibleFields.isEmpty) {
      return const Center(
        child: Text('所有数据系列已隐藏，点击图例可切换显示状态'),
      );
    }

    const String xKey = '时间';
    // 创建图表变量
    final variables = <String, Variable>{};
    // 创建图表元素
    final marks = <Mark>[];

    variables[xKey] = Variable(
      accessor: (datum) => datum['timestamp'] as DateTime,
      scale: TimeScale(
        formatter: (value) => DateFormat('yyyy-MM-dd HH:mm').format(value),
      ),
    );
    for (var i = 0; i < visibleFields.length; i++) {
      final item = visibleFields[i];
      final field = item.key;
      variables[item.key] = Variable(
        accessor: (datum) => datum[item.key] as num,
        scale: LinearScale(
          title: item.label,
          tickCount: null,
          niceRange: true,
          formatter: (value) {
            final formattedValue = (value).toStringAsFixed(2);
            // 在格式化的值后添加单位
            return formattedValue;
          },
        ),
      );
      marks.add(
        LineMark(
          position: Varset(xKey) * Varset(field),
          color: ColorEncode(value: item.color),
          size: SizeEncode(value: 2),
          shape: ShapeEncode(value: BasicLineShape(smooth: true)),
        ),
      );
      marks.add(
        PointMark(
          position: Varset(xKey) * Varset(field),
          color: ColorEncode(value: item.color),
          size: SizeEncode(value: 5),
        ),
      );
    }

    return Chart(
      data: _chartData,
      variables: variables,
      marks: marks,
      axes: [
        Defaults.horizontalAxis,
        Defaults.verticalAxis,
      ],
      selections: {
        'hover': PointSelection(
          on: {
            GestureType.hover, // 桌面端
            GestureType.tapDown, // 点击
            GestureType.longPressMoveUpdate, // 长按并移动
          },
          dim: Dim.x, // 只在x轴方向上拾取
        ),
      },
      tooltip: TooltipGuide(
        followPointer: [true, true],
        align: Alignment.topRight,
      ),
      crosshair:
          CrosshairGuide(followPointer: [true, true], showLabel: [true, true]),
      coord: RectCoord(
        horizontalRangeUpdater: Defaults.horizontalRangeFocusEvent,
      ),
    );
  }
}
