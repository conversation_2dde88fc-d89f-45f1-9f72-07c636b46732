import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:graphic/graphic.dart';


class InteractionStreamDynamicPage extends StatefulWidget {
  const InteractionStreamDynamicPage({Key? key}) : super(key: key);

  @override
  InteractionStreamDynamicPageState createState() =>
      InteractionStreamDynamicPageState();
}

class InteractionStreamDynamicPageState
    extends State<InteractionStreamDynamicPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  final rdm = Random();

  List<Map> data = [];

  late Timer timer;

  final priceVolumeStream = StreamController<GestureEvent>.broadcast();

  final heatmapStream = StreamController<Selected?>.broadcast();

  @override
  void initState() {
    data = [
      {'genre': 'Sports', 'sold': rdm.nextInt(300)},
      {'genre': 'Strategy', 'sold': rdm.nextInt(300)},
      {'genre': 'Action', 'sold': rdm.nextInt(300)},
      {'genre': 'Shooter', 'sold': rdm.nextInt(300)},
      {'genre': 'Other', 'sold': rdm.nextInt(300)},
    ];

    timer = Timer.periodic(const Duration(seconds: 1), (_) {
      setState(() {
        data = [
          {'genre': 'Sports', 'sold': rdm.nextInt(300)},
          {'genre': 'Strategy', 'sold': rdm.nextInt(300)},
          {'genre': 'Action', 'sold': rdm.nextInt(300)},
          {'genre': 'Shooter', 'sold': rdm.nextInt(300)},
          {'genre': 'Other', 'sold': rdm.nextInt(300)},
        ];
      });
    });

    super.initState();
  }

  @override
  void dispose() {
    timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        title: const Text('Dynamic'),
      ),
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Center(
          child: Column(
            children: <Widget>[
              Container(
                padding: const EdgeInsets.fromLTRB(20, 40, 20, 5),
                child: const Text(
                  'Event Stream coupling',
                  style: TextStyle(fontSize: 20),
                ),
              ),
              Container(
                padding: const EdgeInsets.fromLTRB(10, 5, 10, 0),
                alignment: Alignment.centerLeft,
                child: const Text(
                  '- The price chart and volume chart share the same gesture event stream.',
                ),
              ),
              Container(
                margin: const EdgeInsets.only(top: 10),
                width: 350,
                height: 150,
                child: Chart(
                  padding: (_) => const EdgeInsets.fromLTRB(40, 5, 10, 0),
                  rebuild: false,
                  data: priceVolumeData,
                  variables: {
                    'time': Variable(
                      accessor: (Map map) => map['time'] as String,
                      scale: OrdinalScale(tickCount: 3),
                    ),
                    'end': Variable(
                      accessor: (Map map) => map['end'] as num,
                      scale: LinearScale(min: 5, tickCount: 5),
                    ),
                  },
                  marks: [
                    LineMark(
                      size: SizeEncode(value: 1),
                    )
                  ],
                  axes: [
                    Defaults.horizontalAxis
                      ..label = null
                      ..line = null,
                    Defaults.verticalAxis
                      ..gridMapper = (_, index, __) =>
                      index == 0 ? null : Defaults.strokeStyle,
                  ],
                  selections: {
                    'touchMove': PointSelection(
                      on: {
                        GestureType.scaleUpdate,
                        GestureType.tapDown,
                        GestureType.longPressMoveUpdate
                      },
                      dim: Dim.x,
                    )
                  },
                  crosshair: CrosshairGuide(
                    followPointer: [true, false],
                    styles: [
                      PaintStyle(
                          strokeColor: const Color(0xffbfbfbf), dash: [4, 2]),
                      PaintStyle(
                          strokeColor: const Color(0xffbfbfbf), dash: [4, 2]),
                    ],
                  ),
                  gestureStream: priceVolumeStream,
                ),
              ),
              Container(
                margin: const EdgeInsets.only(top: 0),
                width: 350,
                height: 80,
                child: Chart(
                  padding: (_) => const EdgeInsets.fromLTRB(40, 0, 10, 20),
                  rebuild: false,
                  data: priceVolumeData,
                  variables: {
                    'time': Variable(
                      accessor: (Map map) => map['time'] as String,
                      scale: OrdinalScale(tickCount: 3),
                    ),
                    'volume': Variable(
                      accessor: (Map map) => map['volume'] as num,
                      scale: LinearScale(min: 0),
                    ),
                  },
                  marks: [
                    IntervalMark(
                      size: SizeEncode(value: 1),
                    )
                  ],
                  axes: [
                    Defaults.horizontalAxis,
                  ],
                  selections: {
                    'touchMove': PointSelection(
                      on: {
                        GestureType.scaleUpdate,
                        GestureType.tapDown,
                        GestureType.longPressMoveUpdate
                      },
                      dim: Dim.x,
                    )
                  },
                  crosshair: CrosshairGuide(
                    followPointer: [true, false],
                    styles: [
                      PaintStyle(
                          strokeColor: const Color(0xffbfbfbf), dash: [4, 2]),
                      PaintStyle(
                          strokeColor: const Color(0xffbfbfbf), dash: [4, 2]),
                    ],
                  ),
                  gestureStream: priceVolumeStream,
                ),
              ),
              Container(
                padding: const EdgeInsets.fromLTRB(20, 40, 20, 5),
                child: const Text(
                  'Selection Stream coupling',
                  style: TextStyle(fontSize: 20),
                ),
              ),
              Container(
                padding: const EdgeInsets.fromLTRB(10, 5, 10, 0),
                alignment: Alignment.centerLeft,
                child: const Text(
                  '- The above and below heatmaps share the same selection stream. Tap either one to try.',
                ),
              ),
              Container(
                margin: const EdgeInsets.only(top: 10),
                width: 350,
                height: 200,
                child: Chart(
                  padding: (_) => EdgeInsets.zero,
                  data: heatmapData,
                  variables: {
                    'name': Variable(
                      accessor: (List datum) => datum[0].toString(),
                    ),
                    'day': Variable(
                      accessor: (List datum) => datum[1].toString(),
                    ),
                    'sales': Variable(
                      accessor: (List datum) => datum[2] as num,
                    ),
                  },
                  marks: [
                    PolygonMark(
                      shape: ShapeEncode(value: HeatmapShape(sector: true)),
                      color: ColorEncode(
                        variable: 'sales',
                        values: [
                          const Color(0xffbae7ff),
                          const Color(0xff1890ff),
                          const Color(0xff0050b3)
                        ],
                        updaters: {
                          'tap': {false: (color) => color.withAlpha(70)}
                        },
                      ),
                      selectionStream: heatmapStream,
                    )
                  ],
                  coord: PolarCoord(),
                  selections: {'tap': PointSelection()},
                  tooltip: TooltipGuide(
                    anchor: (_) => Offset.zero,
                    align: Alignment.bottomRight,
                  ),
                ),
              ),
              Container(
                margin: const EdgeInsets.only(top: 10),
                width: 350,
                height: 100,
                child: Chart(
                  padding: (_) => EdgeInsets.zero,
                  data: heatmapData,
                  variables: {
                    'name': Variable(
                      accessor: (List datum) => datum[0].toString(),
                    ),
                    'day': Variable(
                      accessor: (List datum) => datum[1].toString(),
                    ),
                    'sales': Variable(
                      accessor: (List datum) => datum[2] as num,
                    ),
                  },
                  marks: [
                    PolygonMark(
                      color: ColorEncode(
                        variable: 'sales',
                        values: [
                          const Color(0xffbae7ff),
                          const Color(0xff1890ff),
                          const Color(0xff0050b3)
                        ],
                        updaters: {
                          'tap': {false: (color) => color.withAlpha(70)}
                        },
                      ),
                      selectionStream: heatmapStream,
                    )
                  ],
                  selections: {'tap': PointSelection()},
                ),
              ),
              Container(
                padding: const EdgeInsets.fromLTRB(20, 40, 20, 5),
                child: const Text(
                  'Auto update',
                  style: TextStyle(fontSize: 20),
                ),
              ),
              Container(
                padding: const EdgeInsets.fromLTRB(10, 5, 10, 0),
                alignment: Alignment.centerLeft,
                child: const Text(
                  '- Change data in every second.',
                ),
              ),
              Container(
                margin: const EdgeInsets.only(top: 10),
                width: 650,
                height: 300,
                child: Chart(
                  rebuild: false,
                  data: data,
                  variables: {
                    'genre': Variable(
                      accessor: (Map map) => map['genre'] as String,
                    ),
                    'sold': Variable(
                      accessor: (Map map) => map['sold'] as num,
                    ),
                  },
                  marks: [IntervalMark()],
                  axes: [
                    Defaults.horizontalAxis,
                    Defaults.verticalAxis,
                  ],
                  selections: {
                    'tap': PointSelection(
                      on: {
                        GestureType.hover,
                        GestureType.tap,
                      },
                      dim: Dim.x,
                    )
                  },
                  tooltip: TooltipGuide(
                    backgroundColor: Colors.black,
                    elevation: 5,
                    textStyle: Defaults.textStyle,
                    variables: ['genre', 'sold'],
                  ),
                  crosshair: CrosshairGuide(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}


const basicData = [
  {'genre': 'Sports', 'sold': 275},
  {'genre': 'Strategy', 'sold': 115},
  {'genre': 'Action', 'sold': 120},
  {'genre': 'Shooter', 'sold': 350},
  {'genre': 'Other', 'sold': 150},
];

const complexGroupData = [
  {'date': '2021-10-01', 'name': 'Liam', 'points': 1468},
  {'date': '2021-10-01', 'name': 'Oliver', 'points': 1487},
  {'date': '2021-10-01', 'name': 'Elijah', 'points': 1494},
  {'date': '2021-10-02', 'name': 'Liam', 'points': 1526},
  {'date': '2021-10-02', 'name': 'Noah', 'points': 1492},
  {'date': '2021-10-02', 'name': 'Oliver', 'points': 1470},
  {'date': '2021-10-02', 'name': 'Elijah', 'points': 1477},
  {'date': '2021-10-03', 'name': 'Liam', 'points': 1466},
  {'date': '2021-10-03', 'name': 'Noah', 'points': 1465},
  {'date': '2021-10-03', 'name': 'Oliver', 'points': 1524},
  {'date': '2021-10-03', 'name': 'Elijah', 'points': 1534},
  {'date': '2021-10-04', 'name': 'Noah', 'points': 1504},
  {'date': '2021-10-04', 'name': 'Elijah', 'points': 1524},
  {'date': '2021-10-05', 'name': 'Oliver', 'points': 1534},
  {'date': '2021-10-06', 'name': 'Noah', 'points': 1463},
  {'date': '2021-10-07', 'name': 'Liam', 'points': 1502},
  {'date': '2021-10-07', 'name': 'Noah', 'points': 1539},
  {'date': '2021-10-08', 'name': 'Liam', 'points': 1476},
  {'date': '2021-10-08', 'name': 'Noah', 'points': 1483},
  {'date': '2021-10-08', 'name': 'Oliver', 'points': 1534},
  {'date': '2021-10-08', 'name': 'Elijah', 'points': 1530},
  {'date': '2021-10-09', 'name': 'Noah', 'points': 1519},
  {'date': '2021-10-09', 'name': 'Oliver', 'points': 1497},
  {'date': '2021-10-09', 'name': 'Elijah', 'points': 1460},
  {'date': '2021-10-10', 'name': 'Liam', 'points': 1514},
  {'date': '2021-10-10', 'name': 'Noah', 'points': 1518},
  {'date': '2021-10-10', 'name': 'Oliver', 'points': 1470},
  {'date': '2021-10-10', 'name': 'Elijah', 'points': 1526},
  {'date': '2021-10-11', 'name': 'Liam', 'points': 1517},
  {'date': '2021-10-11', 'name': 'Noah', 'points': 1478},
  {'date': '2021-10-11', 'name': 'Oliver', 'points': 1468},
  {'date': '2021-10-11', 'name': 'Elijah', 'points': 1487},
  {'date': '2021-10-12', 'name': 'Liam', 'points': 1535},
  {'date': '2021-10-12', 'name': 'Noah', 'points': 1537},
  {'date': '2021-10-12', 'name': 'Oliver', 'points': 1463},
  {'date': '2021-10-12', 'name': 'Elijah', 'points': 1478},
  {'date': '2021-10-13', 'name': 'Oliver', 'points': 1524},
  {'date': '2021-10-13', 'name': 'Elijah', 'points': 1496},
  {'date': '2021-10-14', 'name': 'Liam', 'points': 1527},
  {'date': '2021-10-14', 'name': 'Oliver', 'points': 1527},
  {'date': '2021-10-14', 'name': 'Elijah', 'points': 1462},
  {'date': '2021-10-15', 'name': 'Liam', 'points': 1532},
  {'date': '2021-10-15', 'name': 'Noah', 'points': 1509},
  {'date': '2021-10-15', 'name': 'Oliver', 'points': 1540},
  {'date': '2021-10-15', 'name': 'Elijah', 'points': 1536},
  {'date': '2021-10-16', 'name': 'Liam', 'points': 1480},
  {'date': '2021-10-16', 'name': 'Elijah', 'points': 1533},
  {'date': '2021-10-17', 'name': 'Noah', 'points': 1515},
  {'date': '2021-10-17', 'name': 'Oliver', 'points': 1518},
  {'date': '2021-10-17', 'name': 'Elijah', 'points': 1515},
  {'date': '2021-10-18', 'name': 'Oliver', 'points': 1489},
  {'date': '2021-10-18', 'name': 'Elijah', 'points': 1518},
  {'date': '2021-10-19', 'name': 'Oliver', 'points': 1472},
  {'date': '2021-10-19', 'name': 'Elijah', 'points': 1473},
  {'date': '2021-10-20', 'name': 'Liam', 'points': 1513},
  {'date': '2021-10-20', 'name': 'Noah', 'points': 1533},
  {'date': '2021-10-20', 'name': 'Oliver', 'points': 1487},
  {'date': '2021-10-20', 'name': 'Elijah', 'points': 1532},
  {'date': '2021-10-21', 'name': 'Liam', 'points': 1497},
  {'date': '2021-10-21', 'name': 'Noah', 'points': 1477},
  {'date': '2021-10-21', 'name': 'Oliver', 'points': 1516},
  {'date': '2021-10-22', 'name': 'Liam', 'points': 1466},
  {'date': '2021-10-22', 'name': 'Noah', 'points': 1476},
  {'date': '2021-10-22', 'name': 'Oliver', 'points': 1536},
  {'date': '2021-10-22', 'name': 'Elijah', 'points': 1483},
  {'date': '2021-10-23', 'name': 'Liam', 'points': 1503},
  {'date': '2021-10-23', 'name': 'Oliver', 'points': 1521},
  {'date': '2021-10-23', 'name': 'Elijah', 'points': 1529},
  {'date': '2021-10-24', 'name': 'Liam', 'points': 1460},
  {'date': '2021-10-24', 'name': 'Noah', 'points': 1532},
  {'date': '2021-10-24', 'name': 'Oliver', 'points': 1477},
  {'date': '2021-10-24', 'name': 'Elijah', 'points': 1470},
  {'date': '2021-10-25', 'name': 'Noah', 'points': 1504},
  {'date': '2021-10-25', 'name': 'Oliver', 'points': 1494},
  {'date': '2021-10-25', 'name': 'Elijah', 'points': 1528},
  {'date': '2021-10-26', 'name': 'Liam', 'points': 1517},
  {'date': '2021-10-26', 'name': 'Noah', 'points': 1503},
  {'date': '2021-10-26', 'name': 'Elijah', 'points': 1507},
  {'date': '2021-10-27', 'name': 'Liam', 'points': 1538},
  {'date': '2021-10-27', 'name': 'Noah', 'points': 1530},
  {'date': '2021-10-27', 'name': 'Oliver', 'points': 1496},
  {'date': '2021-10-27', 'name': 'Elijah', 'points': 1519},
  {'date': '2021-10-28', 'name': 'Liam', 'points': 1511},
  {'date': '2021-10-28', 'name': 'Oliver', 'points': 1500},
  {'date': '2021-10-28', 'name': 'Elijah', 'points': 1519},
  {'date': '2021-10-29', 'name': 'Noah', 'points': 1499},
  {'date': '2021-10-29', 'name': 'Oliver', 'points': 1489},
  {'date': '2021-10-30', 'name': 'Noah', 'points': 1460}
];

class TimeSeriesSales {
  final DateTime time;
  final double sales;

  TimeSeriesSales(this.time, this.sales);
}

final timeSeriesSales = [
  TimeSeriesSales(DateTime(2017, 9, 19), 5),
  TimeSeriesSales(DateTime(2017, 9, 26), 25),
  TimeSeriesSales(DateTime(2017, 10, 3), 100),
  TimeSeriesSales(DateTime(2017, 10, 10), 75),
];

const roseData = [
  {'value': 20, 'name': 'rose 1'},
  {'value': 10, 'name': 'rose 2'},
  {'value': 24, 'name': 'rose 3'},
  {'value': 12, 'name': 'rose 4'},
  {'value': 20, 'name': 'rose 5'},
  {'value': 15, 'name': 'rose 6'},
  {'value': 22, 'name': 'rose 7'},
  {'value': 29, 'name': 'rose 8'},
];

const intervalData = [
  {
    'id': 'No.1',
    'min': 76,
    'max': 100,
  },
  {
    'id': 'No.2',
    'min': 56,
    'max': 108,
  },
  {
    'id': 'No.3',
    'min': 38,
    'max': 129,
  },
  {
    'id': 'No.4',
    'min': 58,
    'max': 155,
  },
  {
    'id': 'No.5',
    'min': 45,
    'max': 120,
  },
  {
    'id': 'No.6',
    'min': 23,
    'max': 99,
  },
  {
    'id': 'No.7',
    'min': 18,
    'max': 56,
  },
  {
    'id': 'No.8',
    'min': 18,
    'max': 34,
  },
];

const adjustData = [
  {"type": "Email", "index": 0, "value": 120},
  {"type": "Email", "index": 1, "value": 132},
  {"type": "Email", "index": 2, "value": 101},
  {"type": "Email", "index": 3, "value": 134},
  {"type": "Email", "index": 4, "value": 90},
  {"type": "Email", "index": 5, "value": 230},
  {"type": "Email", "index": 6, "value": 210},
  {"type": "Affiliate", "index": 0, "value": 220},
  {"type": "Affiliate", "index": 1, "value": 182},
  {"type": "Affiliate", "index": 2, "value": 191},
  {"type": "Affiliate", "index": 3, "value": 234},
  {"type": "Affiliate", "index": 4, "value": 290},
  {"type": "Affiliate", "index": 5, "value": 330},
  {"type": "Affiliate", "index": 6, "value": 310},
  {"type": "Video", "index": 0, "value": 150},
  {"type": "Video", "index": 1, "value": 232},
  {"type": "Video", "index": 2, "value": 201},
  {"type": "Video", "index": 3, "value": 154},
  {"type": "Video", "index": 4, "value": 190},
  {"type": "Video", "index": 5, "value": 330},
  {"type": "Video", "index": 6, "value": 410},
  {"type": "Direct", "index": 0, "value": 320},
  {"type": "Direct", "index": 1, "value": 332},
  {"type": "Direct", "index": 2, "value": 301},
  {"type": "Direct", "index": 3, "value": 334},
  {"type": "Direct", "index": 4, "value": 390},
  {"type": "Direct", "index": 5, "value": 330},
  {"type": "Direct", "index": 6, "value": 320},
  {"type": "Search", "index": 0, "value": 320},
  {"type": "Search", "index": 1, "value": 432},
  {"type": "Search", "index": 2, "value": 401},
  {"type": "Search", "index": 3, "value": 434},
  {"type": "Search", "index": 4, "value": 390},
  {"type": "Search", "index": 5, "value": 430},
  {"type": "Search", "index": 6, "value": 420},
];

const scatterData = [
  [28604, 77, 17096869, 'Australia', 1990],
  [31163, 77.4, 27662440, 'Canada', 1990],
  [1516, 68, 1154605773, 'China', 1990],
  [13670, 74.7, 10582082, 'Cuba', 1990],
  [28599, 75, 4986705, 'Finland', 1990],
  [29476, 77.1, 56943299, 'France', 1990],
  [31476, 75.4, 78958237, 'Germany', 1990],
  [28666, 78.1, 254830, 'Iceland', 1990],
  [1777, 57.7, *********, 'India', 1990],
  [29550, 79.1, *********, 'Japan', 1990],
  [2076, 67.9, 20194354, 'North Korea', 1990],
  [12087, 72, 42972254, 'South Korea', 1990],
  [24021, 75.4, 3397534, 'New Zealand', 1990],
  [43296, 76.8, 4240375, 'Norway', 1990],
  [10088, 70.8, 38195258, 'Poland', 1990],
  [19349, 69.6, *********, 'Russia', 1990],
  [10670, 67.3, 53994605, 'Turkey', 1990],
  [26424, 75.7, 57110117, 'United Kingdom', 1990],
  [37062, 75.4, *********, 'United States', 1990],
  [44056, 81.8, 23968973, 'Australia', 2015],
  [43294, 81.7, 35939927, 'Canada', 2015],
  [13334, 76.9, 1376048943, 'China', 2015],
  [21291, 78.5, 11389562, 'Cuba', 2015],
  [38923, 80.8, 5503457, 'Finland', 2015],
  [37599, 81.9, 64395345, 'France', 2015],
  [44053, 81.1, 80688545, 'Germany', 2015],
  [42182, 82.8, 329425, 'Iceland', 2015],
  [5903, 66.8, 1311050527, 'India', 2015],
  [36162, 83.5, *********, 'Japan', 2015],
  [1390, 71.4, 25155317, 'North Korea', 2015],
  [34644, 80.7, 50293439, 'South Korea', 2015],
  [34186, 80.6, 4528526, 'New Zealand', 2015],
  [64304, 81.6, 5210967, 'Norway', 2015],
  [24787, 77.3, 38611794, 'Poland', 2015],
  [23038, 73.13, *********, 'Russia', 2015],
  [19360, 76.5, 78665830, 'Turkey', 2015],
  [38225, 81.4, 64715810, 'United Kingdom', 2015],
  [53354, 79.1, *********, 'United States', 2015]
];

const heatmapData = [
  [0, 0, 10],
  [0, 1, 19],
  [0, 2, 8],
  [0, 3, 24],
  [0, 4, 67],
  [1, 0, 92],
  [1, 1, 58],
  [1, 2, 78],
  [1, 3, 117],
  [1, 4, 48],
  [2, 0, 35],
  [2, 1, 15],
  [2, 2, 123],
  [2, 3, 64],
  [2, 4, 52],
  [3, 0, 72],
  [3, 1, 132],
  [3, 2, 114],
  [3, 3, 19],
  [3, 4, 16],
  [4, 0, 38],
  [4, 1, 5],
  [4, 2, 8],
  [4, 3, 117],
  [4, 4, 115],
  [5, 0, 88],
  [5, 1, 32],
  [5, 2, 12],
  [5, 3, 6],
  [5, 4, 120],
  [6, 0, 13],
  [6, 1, 44],
  [6, 2, 88],
  [6, 3, 98],
  [6, 4, 96],
  [7, 0, 31],
  [7, 1, 1],
  [7, 2, 82],
  [7, 3, 32],
  [7, 4, 30],
  [8, 0, 85],
  [8, 1, 97],
  [8, 2, 123],
  [8, 3, 64],
  [8, 4, 84],
  [9, 0, 47],
  [9, 1, 114],
  [9, 2, 31],
  [9, 3, 48],
  [9, 4, 91]
];

const boxData = [
  {'x': 'Oceania', 'low': 1, 'q1': 9, 'median': 16, 'q3': 22, 'high': 24},
  {'x': 'East Europe', 'low': 1, 'q1': 5, 'median': 8, 'q3': 12, 'high': 16},
  {'x': 'Australia', 'low': 1, 'q1': 8, 'median': 12, 'q3': 19, 'high': 26},
  {'x': 'South America', 'low': 2, 'q1': 8, 'median': 12, 'q3': 21, 'high': 28},
  {'x': 'North Africa', 'low': 1, 'q1': 8, 'median': 14, 'q3': 18, 'high': 24},
  {
    'x': 'North America',
    'low': 3,
    'q1': 10,
    'median': 17,
    'q3': 28,
    'high': 30
  },
  {'x': 'West Europe', 'low': 1, 'q1': 7, 'median': 10, 'q3': 17, 'high': 22},
  {'x': 'West Africa', 'low': 1, 'q1': 6, 'median': 8, 'q3': 13, 'high': 16}
];

const stockData = [
  {
    'time': '2015-11-19',
    'start': 8.18,
    'max': 8.33,
    'min': 7.98,
    'end': 8.32,
    'volume': 1810,
    'money': 14723.56
  },
  {
    'time': '2015-11-18',
    'start': 8.37,
    'max': 8.6,
    'min': 8.03,
    'end': 8.09,
    'volume': 2790.37,
    'money': 23309.19
  },
  {
    'time': '2015-11-17',
    'start': 8.7,
    'max': 8.78,
    'min': 8.32,
    'end': 8.37,
    'volume': 3729.04,
    'money': 31709.71
  },
  {
    'time': '2015-11-16',
    'start': 8.18,
    'max': 8.69,
    'min': 8.05,
    'end': 8.62,
    'volume': 3095.44,
    'money': 26100.69
  },
  {
    'time': '2015-11-13',
    'start': 8.01,
    'max': 8.75,
    'min': 7.97,
    'end': 8.41,
    'volume': 5815.58,
    'money': 48562.37
  },
  {
    'time': '2015-11-12',
    'start': 7.76,
    'max': 8.18,
    'min': 7.61,
    'end': 8.15,
    'volume': 4742.6,
    'money': 37565.36
  },
  {
    'time': '2015-11-11',
    'start': 7.55,
    'max': 7.81,
    'min': 7.49,
    'end': 7.8,
    'volume': 3133.82,
    'money': 24065.42
  },
  {
    'time': '2015-11-10',
    'start': 7.5,
    'max': 7.68,
    'min': 7.44,
    'end': 7.57,
    'volume': 2670.35,
    'money': 20210.58
  },
  {
    'time': '2015-11-09',
    'start': 7.65,
    'max': 7.66,
    'min': 7.3,
    'end': 7.58,
    'volume': 2841.79,
    'money': 21344.36
  },
  {
    'time': '2015-11-06',
    'start': 7.52,
    'max': 7.71,
    'min': 7.48,
    'end': 7.64,
    'volume': 2725.44,
    'money': 20721.51
  },
  {
    'time': '2015-11-05',
    'start': 7.48,
    'max': 7.57,
    'min': 7.29,
    'end': 7.48,
    'volume': 3520.85,
    'money': 26140.83
  },
  {
    'time': '2015-11-04',
    'start': 7.01,
    'max': 7.5,
    'min': 7.01,
    'end': 7.46,
    'volume': 3591.47,
    'money': 26285.52
  },
  {
    'time': '2015-11-03',
    'start': 7.1,
    'max': 7.17,
    'min': 6.82,
    'end': 7,
    'volume': 2029.21,
    'money': 14202.33
  },
  {
    'time': '2015-11-02',
    'start': 7.09,
    'max': 7.44,
    'min': 6.93,
    'end': 7.17,
    'volume': 3191.31,
    'money': 23205.11
  },
  {
    'time': '2015-10-30',
    'start': 6.98,
    'max': 7.27,
    'min': 6.84,
    'end': 7.18,
    'volume': 3522.61,
    'money': 25083.44
  },
  {
    'time': '2015-10-29',
    'start': 6.94,
    'max': 7.2,
    'min': 6.8,
    'end': 7.05,
    'volume': 2752.27,
    'money': 19328.44
  },
  {
    'time': '2015-10-28',
    'start': 7.01,
    'max': 7.14,
    'min': 6.8,
    'end': 6.85,
    'volume': 2311.11,
    'money': 16137.32
  },
  {
    'time': '2015-10-27',
    'start': 6.91,
    'max': 7.31,
    'min': 6.48,
    'end': 7.18,
    'volume': 3172.9,
    'money': 21827.3
  },
  {
    'time': '2015-10-26',
    'start': 6.9,
    'max': 7.08,
    'min': 6.87,
    'end': 6.95,
    'volume': 2769.31,
    'money': 19337.44
  },
  {
    'time': '2015-10-23',
    'start': 6.71,
    'max': 6.85,
    'min': 6.58,
    'end': 6.79,
    'volume': 2483.18,
    'money': 16714.31
  },
  {
    'time': '2015-10-22',
    'start': 6.38,
    'max': 6.67,
    'min': 6.34,
    'end': 6.65,
    'volume': 2225.88,
    'money': 14465.56
  },
];

const lineData = [
  {"Date": "04.01.2016", "Close": 126.12},
  {"Date": "05.01.2016", "Close": 125.688},
  {"Date": "06.01.2016", "Close": 119.704},
  {"Date": "07.01.2016", "Close": 120.19},
  {"Date": "08.01.2016", "Close": 121.157},
  {"Date": "11.01.2016", "Close": 117},
  {"Date": "12.01.2016", "Close": 111.487},
  {"Date": "13.01.2016", "Close": 122},
  {"Date": "14.01.2016", "Close": 117.76},
  {"Date": "15.01.2016", "Close": 114.397},
  {"Date": "18.01.2016", "Close": 116.373},
  {"Date": "19.01.2016", "Close": 120.547},
  {"Date": "20.01.2016", "Close": 113.733},
  {"Date": "21.01.2016", "Close": 114.098},
  {"Date": "22.01.2016", "Close": 123.833},
  {"Date": "25.01.2016", "Close": 125},
  {"Date": "26.01.2016", "Close": 124.866},
  {"Date": "27.01.2016", "Close": 120.264},
  {"Date": "28.01.2016", "Close": 122.296},
  {"Date": "29.01.2016", "Close": 124.502},
  {"Date": "01.02.2016", "Close": 127.936},
  {"Date": "02.02.2016", "Close": 132.513},
  {"Date": "03.02.2016", "Close": 129.95},
  {"Date": "04.02.2016", "Close": 129.275},
  {"Date": "05.02.2016", "Close": 127.898},
  {"Date": "08.02.2016", "Close": 134.9},
  {"Date": "09.02.2016", "Close": 122.819},
  {"Date": "10.02.2016", "Close": 120.108},
  {"Date": "11.02.2016", "Close": 119.447},
  {"Date": "12.02.2016", "Close": 117.8},
  {"Date": "15.02.2016", "Close": 122.8},
  {"Date": "16.02.2016", "Close": 121.865},
  {"Date": "17.02.2016", "Close": 126.3},
  {"Date": "18.02.2016", "Close": 128.259},
  {"Date": "19.02.2016", "Close": 125.724},
  {"Date": "22.02.2016", "Close": 130},
  {"Date": "23.02.2016", "Close": 129.948},
  {"Date": "24.02.2016", "Close": 132.5},
  {"Date": "25.02.2016", "Close": 128.08},
  {"Date": "26.02.2016", "Close": 122},
  {"Date": "29.02.2016", "Close": 122},
  {"Date": "01.03.2016", "Close": 123.449},
  {"Date": "02.03.2016", "Close": 130.139},
  {"Date": "03.03.2016", "Close": 132},
  {"Date": "04.03.2016", "Close": 135},
  {"Date": "07.03.2016", "Close": 123.905},
  {"Date": "08.03.2016", "Close": 125.155},
  {"Date": "09.03.2016", "Close": 126},
  {"Date": "10.03.2016", "Close": 126.778},
  {"Date": "11.03.2016", "Close": 129.656},
  {"Date": "14.03.2016", "Close": 127.64},
  {"Date": "15.03.2016", "Close": 124.786},
  {"Date": "16.03.2016", "Close": 124.469},
  {"Date": "17.03.2016", "Close": 123.5},
  {"Date": "18.03.2016", "Close": 124.061},
  {"Date": "21.03.2016", "Close": 123.5},
  {"Date": "22.03.2016", "Close": 129.002},
  {"Date": "23.03.2016", "Close": 129},
  {"Date": "24.03.2016", "Close": 131.31},
  {"Date": "29.03.2016", "Close": 133.354},
  {"Date": "30.03.2016", "Close": 129.298},
  {"Date": "31.03.2016", "Close": 127.4},
  {"Date": "01.04.2016", "Close": 122.376},
  {"Date": "04.04.2016", "Close": 119.467},
  {"Date": "05.04.2016", "Close": 120.695},
  {"Date": "06.04.2016", "Close": 118.725},
  {"Date": "07.04.2016", "Close": 127.539},
  {"Date": "08.04.2016", "Close": 129.8},
  {"Date": "11.04.2016", "Close": 129.5},
  {"Date": "12.04.2016", "Close": 134.465},
  {"Date": "13.04.2016", "Close": 133},
  {"Date": "14.04.2016", "Close": 137.35},
  {"Date": "15.04.2016", "Close": 137.2},
  {"Date": "18.04.2016", "Close": 132.611},
  {"Date": "19.04.2016", "Close": 135.479},
  {"Date": "20.04.2016", "Close": 139.05},
  {"Date": "21.04.2016", "Close": 142},
  {"Date": "22.04.2016", "Close": 135.761},
  {"Date": "25.04.2016", "Close": 136.174},
  {"Date": "26.04.2016", "Close": 134.782},
  {"Date": "27.04.2016", "Close": 128},
  {"Date": "28.04.2016", "Close": 121.5},
  {"Date": "29.04.2016", "Close": 120},
  {"Date": "02.05.2016", "Close": 123.966},
  {"Date": "03.05.2016", "Close": 122.538},
  {"Date": "04.05.2016", "Close": 120},
  {"Date": "05.05.2016", "Close": 120.21},
  {"Date": "06.05.2016", "Close": 121.01},
  {"Date": "09.05.2016", "Close": 125.4},
  {"Date": "10.05.2016", "Close": 120.622},
  {"Date": "11.05.2016", "Close": 123.85},
  {"Date": "12.05.2016", "Close": 122.963},
  {"Date": "13.05.2016", "Close": 126},
  {"Date": "17.05.2016", "Close": 130},
  {"Date": "18.05.2016", "Close": 128.845},
  {"Date": "19.05.2016", "Close": 130.17},
  {"Date": "20.05.2016", "Close": 129.741},
  {"Date": "23.05.2016", "Close": 129.668},
  {"Date": "24.05.2016", "Close": 126.886},
  {"Date": "25.05.2016", "Close": 128.239},
  {"Date": "26.05.2016", "Close": 127.239},
  {"Date": "27.05.2016", "Close": 127.457},
  {"Date": "30.05.2016", "Close": 127.37},
  {"Date": "31.05.2016", "Close": 130.756},
  {"Date": "01.06.2016", "Close": 133.232},
  {"Date": "02.06.2016", "Close": 126.47},
  {"Date": "03.06.2016", "Close": 126.385},
  {"Date": "06.06.2016", "Close": 128.331},
  {"Date": "07.06.2016", "Close": 130.914},
  {"Date": "08.06.2016", "Close": 133},
  {"Date": "09.06.2016", "Close": 133.041},
  {"Date": "10.06.2016", "Close": 133.041},
  {"Date": "13.06.2016", "Close": 129},
  {"Date": "14.06.2016", "Close": 129.166},
  {"Date": "15.06.2016", "Close": 124.687},
  {"Date": "16.06.2016", "Close": 122.77},
  {"Date": "17.06.2016", "Close": 126.461},
  {"Date": "20.06.2016", "Close": 127},
  {"Date": "21.06.2016", "Close": 125.594},
  {"Date": "22.06.2016", "Close": 127.438},
  {"Date": "23.06.2016", "Close": 124.44},
  {"Date": "24.06.2016", "Close": 122.131},
  {"Date": "27.06.2016", "Close": 120.53},
  {"Date": "28.06.2016", "Close": 120.296},
  {"Date": "29.06.2016", "Close": 125.877},
  {"Date": "30.06.2016", "Close": 126.404},
  {"Date": "01.07.2016", "Close": 130.147},
  {"Date": "04.07.2016", "Close": 129.152},
  {"Date": "05.07.2016", "Close": 125.719},
  {"Date": "06.07.2016", "Close": 129.269},
  {"Date": "07.07.2016", "Close": 131.713},
  {"Date": "08.07.2016", "Close": 146.969},
  {"Date": "11.07.2016", "Close": 201.7},
  {"Date": "12.07.2016", "Close": 202.01},
  {"Date": "13.07.2016", "Close": 195.45},
  {"Date": "14.07.2016", "Close": 220.49},
  {"Date": "15.07.2016", "Close": 238.07},
  {"Date": "18.07.2016", "Close": 270.282},
  {"Date": "19.07.2016", "Close": 258.39},
  {"Date": "20.07.2016", "Close": 243.1},
  {"Date": "21.07.2016", "Close": 237},
  {"Date": "22.07.2016", "Close": 208},
  {"Date": "25.07.2016", "Close": 188.02},
  {"Date": "26.07.2016", "Close": 198.65},
  {"Date": "27.07.2016", "Close": 188},
  {"Date": "28.07.2016", "Close": 180.99},
  {"Date": "29.07.2016", "Close": 186},
  {"Date": "01.08.2016", "Close": 181},
  {"Date": "02.08.2016", "Close": 179.33},
  {"Date": "03.08.2016", "Close": 186},
  {"Date": "04.08.2016", "Close": 187.212},
  {"Date": "05.08.2016", "Close": 184.5},
  {"Date": "08.08.2016", "Close": 189.5},
  {"Date": "09.08.2016", "Close": 202.5},
  {"Date": "10.08.2016", "Close": 202.9},
  {"Date": "11.08.2016", "Close": 200.5},
  {"Date": "12.08.2016", "Close": 195.98},
  {"Date": "15.08.2016", "Close": 196},
  {"Date": "16.08.2016", "Close": 192},
  {"Date": "17.08.2016", "Close": 196.3},
  {"Date": "18.08.2016", "Close": 200.25},
  {"Date": "19.08.2016", "Close": 195},
  {"Date": "22.08.2016", "Close": 200.01},
  {"Date": "23.08.2016", "Close": 199.99},
  {"Date": "24.08.2016", "Close": 195.65},
  {"Date": "25.08.2016", "Close": 195.074},
  {"Date": "26.08.2016", "Close": 191.98},
  {"Date": "29.08.2016", "Close": 193.8},
  {"Date": "30.08.2016", "Close": 192.45},
  {"Date": "31.08.2016", "Close": 194},
  {"Date": "01.09.2016", "Close": 199.1},
  {"Date": "02.09.2016", "Close": 206.21},
  {"Date": "05.09.2016", "Close": 201.98},
  {"Date": "06.09.2016", "Close": 201.1},
  {"Date": "07.09.2016", "Close": 245.25},
  {"Date": "08.09.2016", "Close": 238.01},
  {"Date": "09.09.2016", "Close": 235},
  {"Date": "12.09.2016", "Close": 237.98},
  {"Date": "13.09.2016", "Close": 223.55},
  {"Date": "14.09.2016", "Close": 222.05},
  {"Date": "15.09.2016", "Close": 233.26},
  {"Date": "16.09.2016", "Close": 234},
  {"Date": "19.09.2016", "Close": 235.81},
  {"Date": "20.09.2016", "Close": 239.8},
  {"Date": "21.09.2016", "Close": 238.1},
  {"Date": "22.09.2016", "Close": 241.35},
  {"Date": "23.09.2016", "Close": 237.8},
  {"Date": "26.09.2016", "Close": 232.36},
  {"Date": "27.09.2016", "Close": 238.95},
  {"Date": "28.09.2016", "Close": 237.52},
  {"Date": "29.09.2016", "Close": 237.5},
  {"Date": "30.09.2016", "Close": 235},
  {"Date": "04.10.2016", "Close": 230.82},
  {"Date": "05.10.2016", "Close": 233.5},
  {"Date": "06.10.2016", "Close": 227.094},
  {"Date": "07.10.2016", "Close": 227.783},
  {"Date": "10.10.2016", "Close": 232.55},
  {"Date": "11.10.2016", "Close": 229},
  {"Date": "12.10.2016", "Close": 225.6},
  {"Date": "13.10.2016", "Close": 228.45},
  {"Date": "14.10.2016", "Close": 230.19},
  {"Date": "17.10.2016", "Close": 228.375},
  {"Date": "18.10.2016", "Close": 228.05},
  {"Date": "19.10.2016", "Close": 232.85},
  {"Date": "20.10.2016", "Close": 238.45},
  {"Date": "21.10.2016", "Close": 225.05},
  {"Date": "24.10.2016", "Close": 214.747},
  {"Date": "25.10.2016", "Close": 218.5},
  {"Date": "26.10.2016", "Close": 207.95},
  {"Date": "27.10.2016", "Close": 215.764},
  {"Date": "28.10.2016", "Close": 222.98},
  {"Date": "31.10.2016", "Close": 219.458},
  {"Date": "01.11.2016", "Close": 218.522},
  {"Date": "02.11.2016", "Close": 220.714},
  {"Date": "03.11.2016", "Close": 216.305},
  {"Date": "04.11.2016", "Close": 216.25},
  {"Date": "07.11.2016", "Close": 214.85},
  {"Date": "08.11.2016", "Close": 213.45},
  {"Date": "09.11.2016", "Close": 206.045},
  {"Date": "10.11.2016", "Close": 211},
  {"Date": "11.11.2016", "Close": 210.954},
  {"Date": "14.11.2016", "Close": 215.16},
  {"Date": "15.11.2016", "Close": 211.49},
  {"Date": "16.11.2016", "Close": 218},
  {"Date": "17.11.2016", "Close": 223.2},
  {"Date": "18.11.2016", "Close": 229.5},
  {"Date": "21.11.2016", "Close": 233.949},
  {"Date": "22.11.2016", "Close": 237.3},
  {"Date": "23.11.2016", "Close": 241.182},
  {"Date": "24.11.2016", "Close": 234.479},
  {"Date": "25.11.2016", "Close": 232.55},
  {"Date": "28.11.2016", "Close": 238.5},
  {"Date": "29.11.2016", "Close": 233},
  {"Date": "30.11.2016", "Close": 234},
  {"Date": "01.12.2016", "Close": 230.51},
  {"Date": "02.12.2016", "Close": 222.938},
  {"Date": "05.12.2016", "Close": 225.8},
  {"Date": "06.12.2016", "Close": 231},
  {"Date": "07.12.2016", "Close": 232.849},
  {"Date": "08.12.2016", "Close": 234.473},
  {"Date": "09.12.2016", "Close": 241.82},
  {"Date": "12.12.2016", "Close": 242.501},
  {"Date": "13.12.2016", "Close": 234.98},
  {"Date": "14.12.2016", "Close": 229.1},
  {"Date": "15.12.2016", "Close": 227.999},
  {"Date": "16.12.2016", "Close": 207.995},
  {"Date": "19.12.2016", "Close": 201.7},
  {"Date": "20.12.2016", "Close": 205.97},
  {"Date": "21.12.2016", "Close": 197.799},
  {"Date": "22.12.2016", "Close": 189.89},
  {"Date": "23.12.2016", "Close": 190},
  {"Date": "27.12.2016", "Close": 201},
  {"Date": "28.12.2016", "Close": 199.95},
  {"Date": "29.12.2016", "Close": 200.9},
  {"Date": "30.12.2016", "Close": 199},
  {"Date": "02.01.2017", "Close": 199},
  {"Date": "03.01.2017", "Close": 202.8},
  {"Date": "04.01.2017", "Close": 201.6},
  {"Date": "05.01.2017", "Close": 195.1},
  {"Date": "06.01.2017", "Close": 196},
  {"Date": "09.01.2017", "Close": 197.98},
  {"Date": "10.01.2017", "Close": 203},
  {"Date": "11.01.2017", "Close": 207},
  {"Date": "12.01.2017", "Close": 207.9},
  {"Date": "13.01.2017", "Close": 197.95},
  {"Date": "16.01.2017", "Close": 190.55},
  {"Date": "17.01.2017", "Close": 195},
  {"Date": "18.01.2017", "Close": 195.201},
  {"Date": "19.01.2017", "Close": 194.05},
  {"Date": "20.01.2017", "Close": 194},
  {"Date": "23.01.2017", "Close": 194},
  {"Date": "24.01.2017", "Close": 192.05},
  {"Date": "25.01.2017", "Close": 192.5},
  {"Date": "26.01.2017", "Close": 194.98},
  {"Date": "27.01.2017", "Close": 191.32},
  {"Date": "30.01.2017", "Close": 193},
  {"Date": "31.01.2017", "Close": 187},
  {"Date": "01.02.2017", "Close": 187},
  {"Date": "02.02.2017", "Close": 187.45},
  {"Date": "03.02.2017", "Close": 196},
  {"Date": "06.02.2017", "Close": 196.9},
  {"Date": "07.02.2017", "Close": 196.75},
  {"Date": "08.02.2017", "Close": 192},
  {"Date": "09.02.2017", "Close": 195},
  {"Date": "10.02.2017", "Close": 196},
  {"Date": "13.02.2017", "Close": 198.35},
  {"Date": "14.02.2017", "Close": 192},
  {"Date": "15.02.2017", "Close": 191.9},
  {"Date": "16.02.2017", "Close": 193.5},
  {"Date": "17.02.2017", "Close": 194.85},
  {"Date": "20.02.2017", "Close": 194.8},
  {"Date": "21.02.2017", "Close": 193.05},
  {"Date": "22.02.2017", "Close": 194.85},
  {"Date": "23.02.2017", "Close": 195.9},
  {"Date": "24.02.2017", "Close": 197.5},
  {"Date": "27.02.2017", "Close": 197.5},
  {"Date": "28.02.2017", "Close": 199},
  {"Date": "01.03.2017", "Close": 197},
  {"Date": "02.03.2017", "Close": 192},
  {"Date": "03.03.2017", "Close": 199},
  {"Date": "06.03.2017", "Close": 200.5},
  {"Date": "07.03.2017", "Close": 207.5},
  {"Date": "08.03.2017", "Close": 206},
  {"Date": "09.03.2017", "Close": 200.1},
  {"Date": "10.03.2017", "Close": 200.05},
  {"Date": "13.03.2017", "Close": 205.55},
  {"Date": "14.03.2017", "Close": 202},
  {"Date": "15.03.2017", "Close": 202.05},
  {"Date": "16.03.2017", "Close": 207},
  {"Date": "17.03.2017", "Close": 216.95},
  {"Date": "20.03.2017", "Close": 219.8},
  {"Date": "21.03.2017", "Close": 215.05},
  {"Date": "22.03.2017", "Close": 227.998},
  {"Date": "23.03.2017", "Close": 229},
  {"Date": "24.03.2017", "Close": 227.437},
  {"Date": "27.03.2017", "Close": 221.679},
  {"Date": "28.03.2017", "Close": 220},
  {"Date": "29.03.2017", "Close": 222.8},
  {"Date": "30.03.2017", "Close": 217.092},
  {"Date": "31.03.2017", "Close": 215.03},
  {"Date": "03.04.2017", "Close": 224.9},
  {"Date": "04.04.2017", "Close": 220},
  {"Date": "05.04.2017", "Close": 218.075},
  {"Date": "06.04.2017", "Close": 221.1},
  {"Date": "07.04.2017", "Close": 219.49},
  {"Date": "10.04.2017", "Close": 220},
  {"Date": "11.04.2017", "Close": 216.15},
  {"Date": "12.04.2017", "Close": 216.2},
  {"Date": "13.04.2017", "Close": 216.995},
  {"Date": "18.04.2017", "Close": 227.58},
  {"Date": "19.04.2017", "Close": 232.6},
  {"Date": "20.04.2017", "Close": 231.086},
  {"Date": "21.04.2017", "Close": 229.673},
  {"Date": "24.04.2017", "Close": 230.1},
  {"Date": "25.04.2017", "Close": 232.98},
  {"Date": "26.04.2017", "Close": 228.15},
  {"Date": "27.04.2017", "Close": 223.96},
  {"Date": "28.04.2017", "Close": 231},
  {"Date": "02.05.2017", "Close": 229.715},
  {"Date": "03.05.2017", "Close": 229.5},
  {"Date": "04.05.2017", "Close": 230},
  {"Date": "05.05.2017", "Close": 234.1},
  {"Date": "08.05.2017", "Close": 234.5},
  {"Date": "09.05.2017", "Close": 233.5},
  {"Date": "10.05.2017", "Close": 238.9},
  {"Date": "11.05.2017", "Close": 238.95},
  {"Date": "12.05.2017", "Close": 237},
  {"Date": "15.05.2017", "Close": 239},
  {"Date": "16.05.2017", "Close": 237.1},
  {"Date": "17.05.2017", "Close": 241},
  {"Date": "18.05.2017", "Close": 242.6},
  {"Date": "19.05.2017", "Close": 242.1},
  {"Date": "22.05.2017", "Close": 241.7},
  {"Date": "23.05.2017", "Close": 245.15},
  {"Date": "24.05.2017", "Close": 252.05},
  {"Date": "25.05.2017", "Close": 259.8},
  {"Date": "26.05.2017", "Close": 266.05},
  {"Date": "29.05.2017", "Close": 269.05},
  {"Date": "30.05.2017", "Close": 273},
  {"Date": "31.05.2017", "Close": 268.5},
  {"Date": "01.06.2017", "Close": 267.7},
  {"Date": "02.06.2017", "Close": 266.716},
  {"Date": "05.06.2017", "Close": 266.716},
  {"Date": "06.06.2017", "Close": 286.5},
  {"Date": "07.06.2017", "Close": 276.2}
];

const invalidData = [
  {"Date": "04.01.2016", "Close": 126.12},
  {"Date": "05.01.2016", "Close": 125.688},
  {"Date": "06.01.2016", "Close": 119.704},
  {"Date": "07.01.2016", "Close": 120.19},
  {"Date": "08.01.2016", "Close": 121.157},
  {"Date": "11.01.2016", "Close": 117},
  {"Date": "12.01.2016", "Close": 120},
  {"Date": "13.01.2016", "Close": 122},
  {"Date": "14.01.2016", "Close": 117.76},
  {"Date": "15.01.2016", "Close": 114.397},
  {"Date": "18.01.2016", "Close": 116.373},
  {"Date": "19.01.2016", "Close": 120.547},
  {"Date": "20.01.2016", "Close": 113.733},
  {"Date": "21.01.2016", "Close": 114.098},
  {"Date": "22.01.2016", "Close": 123.833},
  {"Date": "25.01.2016", "Close": 125},
  {"Date": "26.01.2016", "Close": 124.866},
  {"Date": "27.01.2016", "Close": 120.264},
  {"Date": "28.01.2016", "Close": 122.296},
  {"Date": "29.01.2016", "Close": 124.502},
  {"Date": "01.02.2016", "Close": 127.936},
  {"Date": "02.02.2016", "Close": null},
  {"Date": "03.02.2016", "Close": 129.95},
  {"Date": "04.02.2016", "Close": 129.275},
  {"Date": "05.02.2016", "Close": 127.898},
  {"Date": "08.02.2016", "Close": 134.9},
  {"Date": "09.02.2016", "Close": 122.819},
  {"Date": "10.02.2016", "Close": 120.108},
  {"Date": "11.02.2016", "Close": 119.447},
  {"Date": "12.02.2016", "Close": 117.8},
  {"Date": "15.02.2016", "Close": 122.8},
  {"Date": "16.02.2016", "Close": 121.865},
  {"Date": "17.02.2016", "Close": 126.3},
  {"Date": "18.02.2016", "Close": 128.259},
  {"Date": "19.02.2016", "Close": 125.724},
  {"Date": "22.02.2016", "Close": 130},
  {"Date": "23.02.2016", "Close": 129.948},
  {"Date": "24.02.2016", "Close": 132.5},
  {"Date": "25.02.2016", "Close": 128.08},
  {"Date": "26.02.2016", "Close": 122},
  {"Date": "29.02.2016", "Close": 122},
  {"Date": "01.03.2016", "Close": 123.449},
  {"Date": "02.03.2016", "Close": double.nan},
  {"Date": "03.03.2016", "Close": 132},
  {"Date": "04.03.2016", "Close": 135},
  {"Date": "07.03.2016", "Close": 123.905},
  {"Date": "08.03.2016", "Close": 125.155},
  {"Date": "09.03.2016", "Close": 126},
  {"Date": "10.03.2016", "Close": 126.778},
  {"Date": "11.03.2016", "Close": 129.656},
  {"Date": "14.03.2016", "Close": 127.64},
  {"Date": "15.03.2016", "Close": 124.786},
  {"Date": "16.03.2016", "Close": 124.469},
  {"Date": "17.03.2016", "Close": 123.5},
  {"Date": "18.03.2016", "Close": 124.061},
  {"Date": "21.03.2016", "Close": 123.5},
  {"Date": "22.03.2016", "Close": 129.002},
  {"Date": "23.03.2016", "Close": 129},
  {"Date": "24.03.2016", "Close": 131.31},
  {"Date": "29.03.2016", "Close": 133},
  {"Date": "30.03.2016", "Close": 129.298},
  {"Date": "31.03.2016", "Close": 127.4},
  {"Date": "01.04.2016", "Close": 122.376},
  {"Date": "04.04.2016", "Close": 119.467},
  {"Date": "05.04.2016", "Close": 120.695},
  {"Date": "06.04.2016", "Close": 118.725},
  {"Date": "07.04.2016", "Close": 127.539},
  {"Date": "08.04.2016", "Close": 129.8},
  {"Date": "11.04.2016", "Close": 129.5},
  {"Date": "12.04.2016", "Close": 134.465},
  {"Date": "13.04.2016", "Close": 133},
  {"Date": "14.04.2016", "Close": 137.35},
  {"Date": "15.04.2016", "Close": 137.2},
  {"Date": "18.04.2016", "Close": 132.611},
  {"Date": "19.04.2016", "Close": 135.479},
  {"Date": "20.04.2016", "Close": 139.05},
  {"Date": "21.04.2016", "Close": 142},
  {"Date": "22.04.2016", "Close": 135.761},
  {"Date": "25.04.2016", "Close": 136.174},
  {"Date": "26.04.2016", "Close": 134.782},
  {"Date": "27.04.2016", "Close": 128},
  {"Date": "28.04.2016", "Close": 121.5},
  {"Date": "29.04.2016", "Close": 120},
  {"Date": "02.05.2016", "Close": 123.966},
  {"Date": "03.05.2016", "Close": 122.538},
  {"Date": "04.05.2016", "Close": 120},
  {"Date": "05.05.2016", "Close": 120.21},
  {"Date": "06.05.2016", "Close": 121.01},
  {"Date": "09.05.2016", "Close": double.nan},
  {"Date": "10.05.2016", "Close": 120.622},
  {"Date": "11.05.2016", "Close": 123.85},
  {"Date": "12.05.2016", "Close": 122.963},
  {"Date": "13.05.2016", "Close": 126},
  {"Date": "17.05.2016", "Close": 130},
  {"Date": "18.05.2016", "Close": 128.845},
  {"Date": "19.05.2016", "Close": 130.17},
  {"Date": "20.05.2016", "Close": 129.741},
  {"Date": "23.05.2016", "Close": 129.668},
  {"Date": "24.05.2016", "Close": 126.886},
  {"Date": "25.05.2016", "Close": 128.239},
  {"Date": "26.05.2016", "Close": 127.239},
  {"Date": "27.05.2016", "Close": 127.457},
  {"Date": "30.05.2016", "Close": 127.37},
  {"Date": "31.05.2016", "Close": 130.756},
  {"Date": "01.06.2016", "Close": 133.232},
  {"Date": "02.06.2016", "Close": 126.47},
  {"Date": "03.06.2016", "Close": 126.385},
  {"Date": "06.06.2016", "Close": 128.331},
  {"Date": "07.06.2016", "Close": 130.914},
  {"Date": "08.06.2016", "Close": 133},
  {"Date": "09.06.2016", "Close": 133.041},
  {"Date": "10.06.2016", "Close": 133.041},
  {"Date": "13.06.2016", "Close": 129},
  {"Date": "14.06.2016", "Close": 129.166},
  {"Date": "15.06.2016", "Close": 124.687},
  {"Date": "16.06.2016", "Close": 122.77},
  {"Date": "17.06.2016", "Close": 126.461},
  {"Date": "20.06.2016", "Close": 127},
  {"Date": "21.06.2016", "Close": 125.594},
  {"Date": "22.06.2016", "Close": 127.438},
  {"Date": "23.06.2016", "Close": 124.44},
  {"Date": "24.06.2016", "Close": 122.131},
  {"Date": "27.06.2016", "Close": 120.53},
  {"Date": "28.06.2016", "Close": 120.296},
  {"Date": "29.06.2016", "Close": 125.877},
  {"Date": "30.06.2016", "Close": 126.404},
];

const riverData = [
  ['2015/11/08', 10, 'DQ'],
  ['2015/11/09', 15, 'DQ'],
  ['2015/11/10', 35, 'DQ'],
  ['2015/11/11', 38, 'DQ'],
  ['2015/11/12', 22, 'DQ'],
  ['2015/11/13', 16, 'DQ'],
  ['2015/11/14', 7, 'DQ'],
  ['2015/11/15', 2, 'DQ'],
  ['2015/11/16', 17, 'DQ'],
  ['2015/11/17', 33, 'DQ'],
  ['2015/11/18', 40, 'DQ'],
  ['2015/11/19', 32, 'DQ'],
  ['2015/11/20', 26, 'DQ'],
  ['2015/11/21', 35, 'DQ'],
  ['2015/11/22', 40, 'DQ'],
  ['2015/11/23', 32, 'DQ'],
  ['2015/11/24', 26, 'DQ'],
  ['2015/11/25', 22, 'DQ'],
  ['2015/11/26', 16, 'DQ'],
  ['2015/11/27', 22, 'DQ'],
  ['2015/11/28', 10, 'DQ'],
  ['2015/11/08', 35, 'TY'],
  ['2015/11/09', 36, 'TY'],
  ['2015/11/10', 37, 'TY'],
  ['2015/11/11', 22, 'TY'],
  ['2015/11/12', 24, 'TY'],
  ['2015/11/13', 26, 'TY'],
  ['2015/11/14', 34, 'TY'],
  ['2015/11/15', 21, 'TY'],
  ['2015/11/16', 18, 'TY'],
  ['2015/11/17', 45, 'TY'],
  ['2015/11/18', 32, 'TY'],
  ['2015/11/19', 35, 'TY'],
  ['2015/11/20', 30, 'TY'],
  ['2015/11/21', 28, 'TY'],
  ['2015/11/22', 27, 'TY'],
  ['2015/11/23', 26, 'TY'],
  ['2015/11/24', 15, 'TY'],
  ['2015/11/25', 30, 'TY'],
  ['2015/11/26', 35, 'TY'],
  ['2015/11/27', 42, 'TY'],
  ['2015/11/28', 42, 'TY'],
  ['2015/11/08', 21, 'SS'],
  ['2015/11/09', 25, 'SS'],
  ['2015/11/10', 27, 'SS'],
  ['2015/11/11', 23, 'SS'],
  ['2015/11/12', 24, 'SS'],
  ['2015/11/13', 21, 'SS'],
  ['2015/11/14', 35, 'SS'],
  ['2015/11/15', 39, 'SS'],
  ['2015/11/16', 40, 'SS'],
  ['2015/11/17', 36, 'SS'],
  ['2015/11/18', 33, 'SS'],
  ['2015/11/19', 43, 'SS'],
  ['2015/11/20', 40, 'SS'],
  ['2015/11/21', 34, 'SS'],
  ['2015/11/22', 28, 'SS'],
  ['2015/11/23', 26, 'SS'],
  ['2015/11/24', 37, 'SS'],
  ['2015/11/25', 41, 'SS'],
  ['2015/11/26', 46, 'SS'],
  ['2015/11/27', 47, 'SS'],
  ['2015/11/28', 41, 'SS'],
  ['2015/11/08', 10, 'QG'],
  ['2015/11/09', 15, 'QG'],
  ['2015/11/10', 35, 'QG'],
  ['2015/11/11', 38, 'QG'],
  ['2015/11/12', 22, 'QG'],
  ['2015/11/13', 16, 'QG'],
  ['2015/11/14', 7, 'QG'],
  ['2015/11/15', 2, 'QG'],
  ['2015/11/16', 17, 'QG'],
  ['2015/11/17', 33, 'QG'],
  ['2015/11/18', 40, 'QG'],
  ['2015/11/19', 32, 'QG'],
  ['2015/11/20', 26, 'QG'],
  ['2015/11/21', 35, 'QG'],
  ['2015/11/22', 40, 'QG'],
  ['2015/11/23', 32, 'QG'],
  ['2015/11/24', 26, 'QG'],
  ['2015/11/25', 22, 'QG'],
  ['2015/11/26', 16, 'QG'],
  ['2015/11/27', 22, 'QG'],
  ['2015/11/28', 10, 'QG'],
  ['2015/11/08', 10, 'SY'],
  ['2015/11/09', 15, 'SY'],
  ['2015/11/10', 35, 'SY'],
  ['2015/11/11', 38, 'SY'],
  ['2015/11/12', 22, 'SY'],
  ['2015/11/13', 16, 'SY'],
  ['2015/11/14', 7, 'SY'],
  ['2015/11/15', 2, 'SY'],
  ['2015/11/16', 17, 'SY'],
  ['2015/11/17', 33, 'SY'],
  ['2015/11/18', 40, 'SY'],
  ['2015/11/19', 32, 'SY'],
  ['2015/11/20', 26, 'SY'],
  ['2015/11/21', 35, 'SY'],
  ['2015/11/22', 4, 'SY'],
  ['2015/11/23', 32, 'SY'],
  ['2015/11/24', 26, 'SY'],
  ['2015/11/25', 22, 'SY'],
  ['2015/11/26', 16, 'SY'],
  ['2015/11/27', 22, 'SY'],
  ['2015/11/28', 10, 'SY'],
  ['2015/11/08', 10, 'DD'],
  ['2015/11/09', 15, 'DD'],
  ['2015/11/10', 35, 'DD'],
  ['2015/11/11', 38, 'DD'],
  ['2015/11/12', 22, 'DD'],
  ['2015/11/13', 16, 'DD'],
  ['2015/11/14', 7, 'DD'],
  ['2015/11/15', 2, 'DD'],
  ['2015/11/16', 17, 'DD'],
  ['2015/11/17', 33, 'DD'],
  ['2015/11/18', 4, 'DD'],
  ['2015/11/19', 32, 'DD'],
  ['2015/11/20', 26, 'DD'],
  ['2015/11/21', 35, 'DD'],
  ['2015/11/22', 40, 'DD'],
  ['2015/11/23', 32, 'DD'],
  ['2015/11/24', 26, 'DD'],
  ['2015/11/25', 22, 'DD'],
  ['2015/11/26', 16, 'DD'],
  ['2015/11/27', 22, 'DD'],
  ['2015/11/28', 10, 'DD']
];

const priceVolumeData = [
  {
    "time": "2015-11-19",
    "start": 8.18,
    "max": 8.33,
    "min": 7.98,
    "end": 8.32,
    "volume": 1810,
    "money": 14723.56
  },
  {
    "time": "2015-11-18",
    "start": 8.37,
    "max": 8.6,
    "min": 8.03,
    "end": 8.09,
    "volume": 2790.37,
    "money": 23309.19
  },
  {
    "time": "2015-11-17",
    "start": 8.7,
    "max": 8.78,
    "min": 8.32,
    "end": 8.37,
    "volume": 3729.04,
    "money": 31709.71
  },
  {
    "time": "2015-11-16",
    "start": 8.18,
    "max": 8.69,
    "min": 8.05,
    "end": 8.62,
    "volume": 3095.44,
    "money": 26100.69
  },
  {
    "time": "2015-11-13",
    "start": 8.01,
    "max": 8.75,
    "min": 7.97,
    "end": 8.41,
    "volume": 5815.58,
    "money": 48562.37
  },
  {
    "time": "2015-11-12",
    "start": 7.76,
    "max": 8.18,
    "min": 7.61,
    "end": 8.15,
    "volume": 4742.6,
    "money": 37565.36
  },
  {
    "time": "2015-11-11",
    "start": 7.55,
    "max": 7.81,
    "min": 7.49,
    "end": 7.8,
    "volume": 3133.82,
    "money": 24065.42
  },
  {
    "time": "2015-11-10",
    "start": 7.5,
    "max": 7.68,
    "min": 7.44,
    "end": 7.57,
    "volume": 2670.35,
    "money": 20210.58
  },
  {
    "time": "2015-11-09",
    "start": 7.65,
    "max": 7.66,
    "min": 7.3,
    "end": 7.58,
    "volume": 2841.79,
    "money": 21344.36
  },
  {
    "time": "2015-11-06",
    "start": 7.52,
    "max": 7.71,
    "min": 7.48,
    "end": 7.64,
    "volume": 2725.44,
    "money": 20721.51
  },
  {
    "time": "2015-11-05",
    "start": 7.48,
    "max": 7.57,
    "min": 7.29,
    "end": 7.48,
    "volume": 3520.85,
    "money": 26140.83
  },
  {
    "time": "2015-11-04",
    "start": 7.01,
    "max": 7.5,
    "min": 7.01,
    "end": 7.46,
    "volume": 3591.47,
    "money": 26285.52
  },
  {
    "time": "2015-11-03",
    "start": 7.1,
    "max": 7.17,
    "min": 6.82,
    "end": 7,
    "volume": 2029.21,
    "money": 14202.33
  },
  {
    "time": "2015-11-02",
    "start": 7.09,
    "max": 7.44,
    "min": 6.93,
    "end": 7.17,
    "volume": 3191.31,
    "money": 23205.11
  },
  {
    "time": "2015-10-30",
    "start": 6.98,
    "max": 7.27,
    "min": 6.84,
    "end": 7.18,
    "volume": 3522.61,
    "money": 25083.44
  },
  {
    "time": "2015-10-29",
    "start": 6.94,
    "max": 7.2,
    "min": 6.8,
    "end": 7.05,
    "volume": 2752.27,
    "money": 19328.44
  },
  {
    "time": "2015-10-28",
    "start": 7.01,
    "max": 7.14,
    "min": 6.8,
    "end": 6.85,
    "volume": 2311.11,
    "money": 16137.32
  },
  {
    "time": "2015-10-27",
    "start": 6.91,
    "max": 7.31,
    "min": 6.48,
    "end": 7.18,
    "volume": 3172.9,
    "money": 21827.3
  },
  {
    "time": "2015-10-26",
    "start": 6.9,
    "max": 7.08,
    "min": 6.87,
    "end": 6.95,
    "volume": 2769.31,
    "money": 19337.44
  },
  {
    "time": "2015-10-23",
    "start": 6.71,
    "max": 6.85,
    "min": 6.58,
    "end": 6.79,
    "volume": 2483.18,
    "money": 16714.31
  },
  {
    "time": "2015-10-22",
    "start": 6.38,
    "max": 6.67,
    "min": 6.34,
    "end": 6.65,
    "volume": 2225.88,
    "money": 14465.56
  },
  {
    "time": "2015-10-21",
    "start": 7.08,
    "max": 7.1,
    "min": 6.41,
    "end": 6.41,
    "volume": 2891.47,
    "money": 19585.98
  },
  {
    "time": "2015-10-20",
    "start": 6.88,
    "max": 7.19,
    "min": 6.85,
    "end": 7.12,
    "volume": 2389.62,
    "money": 16813.58
  },
  {
    "time": "2015-10-19",
    "start": 7.1,
    "max": 7.14,
    "min": 6.8,
    "end": 6.94,
    "volume": 2786.61,
    "money": 19474.72
  },
  {
    "time": "2015-10-16",
    "start": 6.92,
    "max": 7.38,
    "min": 6.73,
    "end": 7.15,
    "volume": 3289.27,
    "money": 22963.97
  },
  {
    "time": "2015-10-15",
    "start": 6.63,
    "max": 6.9,
    "min": 6.6,
    "end": 6.89,
    "volume": 2440.37,
    "money": 16575.84
  },
  {
    "time": "2015-10-14",
    "start": 6.7,
    "max": 6.99,
    "min": 6.61,
    "end": 6.66,
    "volume": 2496.38,
    "money": 16858.28
  },
  {
    "time": "2015-10-13",
    "start": 6.55,
    "max": 6.81,
    "min": 6.55,
    "end": 6.75,
    "volume": 2299.83,
    "money": 15338.24
  },
  {
    "time": "2015-10-12",
    "start": 6.29,
    "max": 6.89,
    "min": 6.29,
    "end": 6.69,
    "volume": 3147.58,
    "money": 20738.35
  },
  {
    "time": "2015-10-09",
    "start": 6.1,
    "max": 6.44,
    "min": 6.08,
    "end": 6.34,
    "volume": 2664.04,
    "money": 16811.14
  },
  {
    "time": "2015-10-08",
    "start": 6.11,
    "max": 6.28,
    "min": 6,
    "end": 6.12,
    "volume": 2197.23,
    "money": 13440.92
  },
  {
    "time": "2015-09-30",
    "start": 5.93,
    "max": 6.12,
    "min": 5.81,
    "end": 5.83,
    "volume": 1459.64,
    "money": 8659.52
  },
  {
    "time": "2015-09-29",
    "start": 5.96,
    "max": 5.98,
    "min": 5.73,
    "end": 5.83,
    "volume": 1047.42,
    "money": 6132.72
  },
  {
    "time": "2015-09-28",
    "start": 5.86,
    "max": 6.13,
    "min": 5.85,
    "end": 6.07,
    "volume": 952.45,
    "money": 5717.33
  },
  {
    "time": "2015-09-25",
    "start": 6.23,
    "max": 6.28,
    "min": 5.85,
    "end": 5.96,
    "volume": 1395.27,
    "money": 8465.95
  },
  {
    "time": "2015-09-24",
    "start": 6.16,
    "max": 6.32,
    "min": 6.1,
    "end": 6.27,
    "volume": 1434.38,
    "money": 8920.88
  },
  {
    "time": "2015-09-23",
    "start": 6.18,
    "max": 6.32,
    "min": 6.02,
    "end": 6.12,
    "volume": 1558.54,
    "money": 9631.38
  },
  {
    "time": "2015-09-22",
    "start": 6.35,
    "max": 6.4,
    "min": 6.15,
    "end": 6.25,
    "volume": 1893.83,
    "money": 11901.51
  },
  {
    "time": "2015-09-21",
    "start": 5.83,
    "max": 6.32,
    "min": 5.83,
    "end": 6.31,
    "volume": 1748.35,
    "money": 10807.66
  },
  {
    "time": "2015-09-18",
    "start": 6,
    "max": 6.1,
    "min": 5.81,
    "end": 6.02,
    "volume": 1507.09,
    "money": 8999.44
  },
  {
    "time": "2015-09-17",
    "start": 6.1,
    "max": 6.34,
    "min": 5.8,
    "end": 5.83,
    "volume": 2135.64,
    "money": 13039.56
  },
  {
    "time": "2015-09-16",
    "start": 5.58,
    "max": 6.07,
    "min": 5.4,
    "end": 6.07,
    "volume": 1758.57,
    "money": 10132.25
  },
  {
    "time": "2015-09-15",
    "start": 5.81,
    "max": 6.09,
    "min": 5.52,
    "end": 5.52,
    "volume": 1617.12,
    "money": 9248.69
  },
  {
    "time": "2015-09-14",
    "start": 6.98,
    "max": 7.06,
    "min": 6.13,
    "end": 6.13,
    "volume": 1982.9,
    "money": 12989.01
  },
  {
    "time": "2015-09-11",
    "start": 6.87,
    "max": 7.01,
    "min": 6.68,
    "end": 6.81,
    "volume": 1371.77,
    "money": 9370.49
  },
  {
    "time": "2015-09-10",
    "start": 6.7,
    "max": 7.17,
    "min": 6.65,
    "end": 6.86,
    "volume": 2181.33,
    "money": 15169.4
  },
  {
    "time": "2015-09-09",
    "start": 6.76,
    "max": 7.03,
    "min": 6.65,
    "end": 6.93,
    "volume": 2105.28,
    "money": 14426.76
  },
  {
    "time": "2015-09-08",
    "start": 6.26,
    "max": 6.7,
    "min": 6.01,
    "end": 6.64,
    "volume": 1506.53,
    "money": 9556.54
  },
  {
    "time": "2015-09-07",
    "start": 6.19,
    "max": 6.45,
    "min": 6.09,
    "end": 6.2,
    "volume": 1605.85,
    "money": 10091.26
  },
  {
    "time": "2015-09-02",
    "start": 6.2,
    "max": 6.84,
    "min": 5.98,
    "end": 5.99,
    "volume": 1934.95,
    "money": 12225.68
  },
  {
    "time": "2015-09-01",
    "start": 7.22,
    "max": 7.28,
    "min": 6.64,
    "end": 6.64,
    "volume": 2645.87,
    "money": 18017.91
  },
  {
    "time": "2015-08-31",
    "start": 7.83,
    "max": 8,
    "min": 7.35,
    "end": 7.38,
    "volume": 2984.03,
    "money": 23031.24
  },
  {
    "time": "2015-08-28",
    "start": 7.3,
    "max": 7.77,
    "min": 7.1,
    "end": 7.77,
    "volume": 3092.6,
    "money": 23121.49
  },
  {
    "time": "2015-08-27",
    "start": 6.75,
    "max": 7.1,
    "min": 6.43,
    "end": 7.06,
    "volume": 2903.76,
    "money": 19848.87
  },
  {
    "time": "2015-08-26",
    "start": 7.13,
    "max": 7.43,
    "min": 6.42,
    "end": 6.58,
    "volume": 4212.04,
    "money": 29069.25
  },
  {
    "time": "2015-08-25",
    "start": 7.13,
    "max": 7.29,
    "min": 7.13,
    "end": 7.13,
    "volume": 1838.59,
    "money": 13140.8
  },
  {
    "time": "2015-08-24",
    "start": 8.4,
    "max": 8.4,
    "min": 7.92,
    "end": 7.92,
    "volume": 1766.39,
    "money": 14168.86
  },
  {
    "time": "2015-08-21",
    "start": 9,
    "max": 9.61,
    "min": 8.53,
    "end": 8.8,
    "volume": 3388.27,
    "money": 30952.11
  },
  {
    "time": "2015-08-20",
    "start": 10.02,
    "max": 10.24,
    "min": 9.32,
    "end": 9.33,
    "volume": 3902.17,
    "money": 38391.68
  },
  {
    "time": "2015-08-19",
    "start": 9.3,
    "max": 10.59,
    "min": 9.01,
    "end": 10.35,
    "volume": 5568.96,
    "money": 53928.79
  },
  {
    "time": "2015-08-18",
    "start": 11.18,
    "max": 11.5,
    "min": 10,
    "end": 10,
    "volume": 7332.7,
    "money": 78439.61
  },
  {
    "time": "2015-08-17",
    "start": 10.2,
    "max": 11.11,
    "min": 9.9,
    "end": 11.11,
    "volume": 8121.86,
    "money": 86298.92
  },
  {
    "time": "2015-08-14",
    "start": 9.58,
    "max": 10.37,
    "min": 9.37,
    "end": 10.1,
    "volume": 6001.61,
    "money": 58425.11
  },
  {
    "time": "2015-08-13",
    "start": 9.14,
    "max": 9.5,
    "min": 8.91,
    "end": 9.49,
    "volume": 3854.19,
    "money": 35696.27
  },
  {
    "time": "2015-08-12",
    "start": 9.09,
    "max": 9.68,
    "min": 8.98,
    "end": 9.29,
    "volume": 4238.57,
    "money": 39909.3
  },
  {
    "time": "2015-08-11",
    "start": 9.23,
    "max": 9.47,
    "min": 9,
    "end": 9.15,
    "volume": 4294.85,
    "money": 39674.71
  },
  {
    "time": "2015-08-10",
    "start": 8.9,
    "max": 9.38,
    "min": 8.76,
    "end": 9.2,
    "volume": 4013.11,
    "money": 36287.89
  },
  {
    "time": "2015-08-07",
    "start": 8.36,
    "max": 8.8,
    "min": 8.31,
    "end": 8.7,
    "volume": 3092.66,
    "money": 26336.76
  },
  {
    "time": "2015-08-06",
    "start": 8.03,
    "max": 8.42,
    "min": 7.95,
    "end": 8.25,
    "volume": 2072.21,
    "money": 17060.11
  },
  {
    "time": "2015-08-05",
    "start": 8.5,
    "max": 8.69,
    "min": 8.08,
    "end": 8.28,
    "volume": 3533.94,
    "money": 29796.96
  },
  {
    "time": "2015-08-04",
    "start": 7.65,
    "max": 8.39,
    "min": 7.65,
    "end": 8.39,
    "volume": 3067.22,
    "money": 24773.88
  },
  {
    "time": "2015-08-03",
    "start": 8.15,
    "max": 8.4,
    "min": 7.6,
    "end": 7.63,
    "volume": 3098.33,
    "money": 24382.99
  },
  {
    "time": "2015-07-31",
    "start": 8.7,
    "max": 9.01,
    "min": 8.25,
    "end": 8.44,
    "volume": 3500.61,
    "money": 30289.83
  },
  {
    "time": "2015-07-30",
    "start": 8.92,
    "max": 9.65,
    "min": 8.7,
    "end": 8.97,
    "volume": 6022.8,
    "money": 55624.85
  },
  {
    "time": "2015-07-29",
    "start": 8.35,
    "max": 8.91,
    "min": 7.78,
    "end": 8.88,
    "volume": 4177.18,
    "money": 34893.2
  },
  {
    "time": "2015-07-28",
    "start": 8,
    "max": 9,
    "min": 7.92,
    "end": 8.1,
    "volume": 5114.55,
    "money": 42095.99
  },
  {
    "time": "2015-07-27",
    "start": 9.4,
    "max": 9.99,
    "min": 8.8,
    "end": 8.8,
    "volume": 6001.51,
    "money": 56962.25
  },
  {
    "time": "2015-07-24",
    "start": 9.27,
    "max": 10.28,
    "min": 9.11,
    "end": 9.78,
    "volume": 8446.76,
    "money": 81991.19
  },
  {
    "time": "2015-07-23",
    "start": 9,
    "max": 9.78,
    "min": 8.74,
    "end": 9.46,
    "volume": 8496.1,
    "money": 77551.01
  },
  {
    "time": "2015-07-22",
    "start": 8.08,
    "max": 8.97,
    "min": 8.05,
    "end": 8.97,
    "volume": 8676.75,
    "money": 75751.1
  },
  {
    "time": "2015-07-21",
    "start": 7.8,
    "max": 8.33,
    "min": 7.65,
    "end": 8.15,
    "volume": 4631.15,
    "money": 37450.78
  },
  {
    "time": "2015-07-20",
    "start": 7.72,
    "max": 8.27,
    "min": 7.63,
    "end": 8.05,
    "volume": 6428.2,
    "money": 51127.98
  },
  {
    "time": "2015-07-17",
    "start": 6.94,
    "max": 7.73,
    "min": 6.94,
    "end": 7.73,
    "volume": 4835.44,
    "money": 36666.58
  },
  {
    "time": "2015-07-16",
    "start": 6.53,
    "max": 7.48,
    "min": 6.42,
    "end": 7.03,
    "volume": 3605.77,
    "money": 25031.15
  },
  {
    "time": "2015-07-15",
    "start": 7.57,
    "max": 7.83,
    "min": 7.13,
    "end": 7.13,
    "volume": 2729.59,
    "money": 20041.75
  },
  {
    "time": "2015-07-14",
    "start": 8.2,
    "max": 8.7,
    "min": 7.66,
    "end": 7.92,
    "volume": 7073.81,
    "money": 58131.16
  },
  {
    "time": "2015-07-13",
    "start": 7.5,
    "max": 8.1,
    "min": 7.5,
    "end": 8.1,
    "volume": 4573.92,
    "money": 36083.69
  },
  {
    "time": "2015-07-10",
    "start": 6.9,
    "max": 7.36,
    "min": 6.88,
    "end": 7.36,
    "volume": 4183.37,
    "money": 30411.19
  },
  {
    "time": "2015-07-09",
    "start": 5.47,
    "max": 6.69,
    "min": 5.47,
    "end": 6.69,
    "volume": 6661.78,
    "money": 40398.96
  },
  {
    "time": "2015-07-08",
    "start": 6.08,
    "max": 6.08,
    "min": 6.08,
    "end": 6.08,
    "volume": 158.16,
    "money": 961.61
  },
  {
    "time": "2015-07-07",
    "start": 6.77,
    "max": 6.88,
    "min": 6.75,
    "end": 6.75,
    "volume": 383.45,
    "money": 2590.85
  },
  {
    "time": "2015-07-06",
    "start": 9.1,
    "max": 9.1,
    "min": 7.5,
    "end": 7.5,
    "volume": 2968.98,
    "money": 24002.6
  },
  {
    "time": "2015-07-03",
    "start": 8.38,
    "max": 8.87,
    "min": 8.33,
    "end": 8.33,
    "volume": 2641.73,
    "money": 22223.44
  },
  {
    "time": "2015-07-02",
    "start": 10.38,
    "max": 10.38,
    "min": 9.26,
    "end": 9.26,
    "volume": 2611.06,
    "money": 24620.81
  },
  {
    "time": "2015-07-01",
    "start": 11.31,
    "max": 11.61,
    "min": 10.29,
    "end": 10.29,
    "volume": 3401.45,
    "money": 37212.87
  },
  {
    "time": "2015-06-30",
    "start": 10.08,
    "max": 11.52,
    "min": 10.01,
    "end": 11.43,
    "volume": 4205.99,
    "money": 45381.06
  },
  {
    "time": "2015-06-29",
    "start": 12.96,
    "max": 12.96,
    "min": 11.12,
    "end": 11.12,
    "volume": 3745.68,
    "money": 44252.47
  },
  {
    "time": "2015-06-26",
    "start": 13.15,
    "max": 13.41,
    "min": 12.36,
    "end": 12.36,
    "volume": 3675.91,
    "money": 46759.29
  },
  {
    "time": "2015-06-25",
    "start": 13.71,
    "max": 14.46,
    "min": 13.3,
    "end": 13.73,
    "volume": 4907.6,
    "money": 68290.5
  },
  {
    "time": "2015-06-24",
    "start": 13.35,
    "max": 13.85,
    "min": 12.9,
    "end": 13.71,
    "volume": 3656.8,
    "money": 49219.92
  },
  {
    "time": "2015-06-23",
    "start": 13.26,
    "max": 13.64,
    "min": 12.26,
    "end": 13.2,
    "volume": 3566.35,
    "money": 45904.78
  },
  {
    "time": "2015-06-19",
    "start": 14.45,
    "max": 14.97,
    "min": 13.62,
    "end": 13.62,
    "volume": 3621.43,
    "money": 51108.31
  },
  {
    "time": "2015-06-18",
    "start": 14.5,
    "max": 16,
    "min": 14.3,
    "end": 15.13,
    "volume": 5046.59,
    "money": 77208.53
  },
  {
    "time": "2015-06-17",
    "start": 14.46,
    "max": 15,
    "min": 14,
    "end": 14.6,
    "volume": 3483.7,
    "money": 50495.84
  },
  {
    "time": "2015-06-16",
    "start": 14,
    "max": 15.1,
    "min": 13.42,
    "end": 14.8,
    "volume": 4844.74,
    "money": 68953.77
  },
  {
    "time": "2015-06-15",
    "start": 14.5,
    "max": 15.09,
    "min": 14.1,
    "end": 14.39,
    "volume": 4008.2,
    "money": 58703.24
  },
  {
    "time": "2015-06-12",
    "start": 14.07,
    "max": 14.97,
    "min": 14,
    "end": 14.37,
    "volume": 5399.47,
    "money": 78592.45
  },
  {
    "time": "2015-06-11",
    "start": 13.4,
    "max": 14.5,
    "min": 13.19,
    "end": 14.13,
    "volume": 5472.93,
    "money": 76037.31
  },
  {
    "time": "2015-06-10",
    "start": 12.95,
    "max": 13.47,
    "min": 12.71,
    "end": 13.37,
    "volume": 4087.74,
    "money": 53951.64
  },
  {
    "time": "2015-06-09",
    "start": 13.46,
    "max": 13.46,
    "min": 12.85,
    "end": 13.12,
    "volume": 4371.67,
    "money": 57352.21
  },
  {
    "time": "2015-06-08",
    "start": 12.88,
    "max": 13.69,
    "min": 12.59,
    "end": 13.61,
    "volume": 7406.58,
    "money": 98236.3
  },
  {
    "time": "2015-06-05",
    "start": 12.38,
    "max": 12.94,
    "min": 12.24,
    "end": 12.77,
    "volume": 5386.66,
    "money": 68208.51
  },
  {
    "time": "2015-06-04",
    "start": 12.55,
    "max": 12.81,
    "min": 11.29,
    "end": 12.31,
    "volume": 3905.22,
    "money": 47415.64
  },
  {
    "time": "2015-06-03",
    "start": 13,
    "max": 13.15,
    "min": 12.2,
    "end": 12.54,
    "volume": 4559.61,
    "money": 57928.58
  },
  {
    "time": "2015-06-02",
    "start": 11.84,
    "max": 12.77,
    "min": 11.48,
    "end": 12.73,
    "volume": 4405.17,
    "money": 52747.92
  },
  {
    "time": "2015-06-01",
    "start": 11.29,
    "max": 11.8,
    "min": 11,
    "end": 11.74,
    "volume": 3308.94,
    "money": 38060.2
  },
  {
    "time": "2015-05-29",
    "start": 11.3,
    "max": 11.65,
    "min": 10.31,
    "end": 11.11,
    "volume": 3434.12,
    "money": 38143.88
  },
  {
    "time": "2015-05-28",
    "start": 12.79,
    "max": 12.99,
    "min": 11.39,
    "end": 11.4,
    "volume": 4979.63,
    "money": 61398.36
  },
  {
    "time": "2015-05-27",
    "start": 12.89,
    "max": 13.18,
    "min": 12.5,
    "end": 12.66,
    "volume": 4886.86,
    "money": 62349.63
  },
  {
    "time": "2015-05-26",
    "start": 12.4,
    "max": 13.08,
    "min": 11.96,
    "end": 12.92,
    "volume": 5838.51,
    "money": 73409.96
  },
  {
    "time": "2015-05-25",
    "start": 11.7,
    "max": 12.87,
    "min": 11.61,
    "end": 12.3,
    "volume": 6405.2,
    "money": 78937.32
  },
  {
    "time": "2015-05-22",
    "start": 11.39,
    "max": 11.89,
    "min": 11.18,
    "end": 11.71,
    "volume": 5519.87,
    "money": 63515.93
  },
  {
    "time": "2015-05-21",
    "start": 11.27,
    "max": 11.35,
    "min": 11.05,
    "end": 11.33,
    "volume": 4132.8,
    "money": 46318.65
  },
  {
    "time": "2015-05-20",
    "start": 11.23,
    "max": 11.48,
    "min": 10.81,
    "end": 11.32,
    "volume": 6859.76,
    "money": 76273.65
  },
  {
    "time": "2015-05-19",
    "start": 11.5,
    "max": 11.78,
    "min": 11,
    "end": 11.33,
    "volume": 6864.05,
    "money": 77731.34
  },
  {
    "time": "2015-05-18",
    "start": 11.68,
    "max": 12.25,
    "min": 11.45,
    "end": 12.15,
    "volume": 4236.5,
    "money": 50728.6
  },
  {
    "time": "2015-05-15",
    "start": 11.19,
    "max": 12.28,
    "min": 10.8,
    "end": 11.69,
    "volume": 5515.66,
    "money": 64496.32
  },
  {
    "time": "2015-05-14",
    "start": 10.18,
    "max": 11.19,
    "min": 10.11,
    "end": 11.19,
    "volume": 4181.77,
    "money": 45399.19
  },
  {
    "time": "2015-05-13",
    "start": 10.2,
    "max": 10.32,
    "min": 10,
    "end": 10.17,
    "volume": 2247.39,
    "money": 22781.23
  },
  {
    "time": "2015-05-12",
    "start": 10.3,
    "max": 10.36,
    "min": 10.01,
    "end": 10.28,
    "volume": 2010.65,
    "money": 20480.63
  },
  {
    "time": "2015-05-11",
    "start": 9.98,
    "max": 10.36,
    "min": 9.89,
    "end": 10.3,
    "volume": 2101.26,
    "money": 21367.53
  },
  {
    "time": "2015-05-08",
    "start": 9.82,
    "max": 10.08,
    "min": 9.65,
    "end": 9.94,
    "volume": 1609.43,
    "money": 15845.56
  },
  {
    "time": "2015-05-07",
    "start": 9.62,
    "max": 9.84,
    "min": 9.45,
    "end": 9.6,
    "volume": 1270.86,
    "money": 12241.17
  },
  {
    "time": "2015-05-06",
    "start": 10.18,
    "max": 10.25,
    "min": 9.6,
    "end": 9.66,
    "volume": 1754.7,
    "money": 17347.05
  },
  {
    "time": "2015-05-05",
    "start": 10.68,
    "max": 10.68,
    "min": 10,
    "end": 10.02,
    "volume": 1903.5,
    "money": 19598.64
  },
  {
    "time": "2015-05-04",
    "start": 10.61,
    "max": 10.84,
    "min": 10.55,
    "end": 10.72,
    "volume": 1554.93,
    "money": 16624.43
  },
  {
    "time": "2015-04-30",
    "start": 10.4,
    "max": 11.05,
    "min": 10.4,
    "end": 10.63,
    "volume": 2169.06,
    "money": 23333.06
  },
  {
    "time": "2015-04-29",
    "start": 10.31,
    "max": 10.64,
    "min": 10.25,
    "end": 10.4,
    "volume": 1614.77,
    "money": 16910.96
  },
  {
    "time": "2015-04-28",
    "start": 11.07,
    "max": 11.25,
    "min": 10.46,
    "end": 10.49,
    "volume": 2552.21,
    "money": 27515.88
  },
  {
    "time": "2015-04-27",
    "start": 10.6,
    "max": 11.67,
    "min": 10.6,
    "end": 11.06,
    "volume": 4216.46,
    "money": 47534.53
  },
  {
    "time": "2015-04-24",
    "start": 10.5,
    "max": 10.85,
    "min": 10.25,
    "end": 10.61,
    "volume": 2326.42,
    "money": 24599.63
  },
  {
    "time": "2015-04-23",
    "start": 10.26,
    "max": 10.93,
    "min": 10.11,
    "end": 10.7,
    "volume": 3767.77,
    "money": 39643.72
  },
  {
    "time": "2015-04-22",
    "start": 10.22,
    "max": 10.42,
    "min": 10.08,
    "end": 10.23,
    "volume": 2868.77,
    "money": 29316.49
  },
  {
    "time": "2015-04-21",
    "start": 9.56,
    "max": 10.2,
    "min": 9.4,
    "end": 10.19,
    "volume": 3493.61,
    "money": 34865.01
  },
  {
    "time": "2015-04-20",
    "start": 9.71,
    "max": 9.99,
    "min": 9.42,
    "end": 9.6,
    "volume": 2462.09,
    "money": 23769.5
  },
  {
    "time": "2015-04-17",
    "start": 9.79,
    "max": 10.09,
    "min": 9.16,
    "end": 9.82,
    "volume": 4473.33,
    "money": 43367.29
  },
  {
    "time": "2015-04-16",
    "start": 9.36,
    "max": 10.04,
    "min": 8.9,
    "end": 9.66,
    "volume": 2851.79,
    "money": 27210.03
  },
  {
    "time": "2015-04-15",
    "start": 10.03,
    "max": 10.28,
    "min": 9.37,
    "end": 9.43,
    "volume": 3138.11,
    "money": 30713.13
  },
  {
    "time": "2015-04-14",
    "start": 10.33,
    "max": 10.33,
    "min": 9.98,
    "end": 10.03,
    "volume": 2951.59,
    "money": 29803.4
  },
  {
    "time": "2015-04-13",
    "start": 10.3,
    "max": 10.63,
    "min": 10.2,
    "end": 10.33,
    "volume": 3196.99,
    "money": 33351.76
  },
  {
    "time": "2015-04-10",
    "start": 10.25,
    "max": 10.5,
    "min": 10,
    "end": 10.28,
    "volume": 2565.64,
    "money": 26337.81
  },
  {
    "time": "2015-04-09",
    "start": 9.78,
    "max": 10.48,
    "min": 9.58,
    "end": 10.22,
    "volume": 4316.86,
    "money": 43647.33
  },
  {
    "time": "2015-04-08",
    "start": 9.46,
    "max": 9.86,
    "min": 9.02,
    "end": 9.78,
    "volume": 3683.43,
    "money": 34664.66
  },
  {
    "time": "2015-04-07",
    "start": 9.53,
    "max": 9.87,
    "min": 9.38,
    "end": 9.44,
    "volume": 3874.06,
    "money": 37076.79
  },
  {
    "time": "2015-04-03",
    "start": 8.6,
    "max": 9.48,
    "min": 8.4,
    "end": 9.48,
    "volume": 3760.78,
    "money": 34361.28
  },
  {
    "time": "2015-04-02",
    "start": 8.45,
    "max": 8.74,
    "min": 8.18,
    "end": 8.62,
    "volume": 3076.83,
    "money": 26112.98
  },
  {
    "time": "2015-04-01",
    "start": 8.16,
    "max": 8.61,
    "min": 8.06,
    "end": 8.45,
    "volume": 2396.89,
    "money": 20000.88
  },
  {
    "time": "2015-03-31",
    "start": 8.18,
    "max": 8.5,
    "min": 8.13,
    "end": 8.16,
    "volume": 1938,
    "money": 15989.33
  },
  {
    "time": "2015-03-30",
    "start": 8.2,
    "max": 8.53,
    "min": 8.11,
    "end": 8.26,
    "volume": 2820.79,
    "money": 23532.99
  },
  {
    "time": "2015-03-27",
    "start": 8.4,
    "max": 8.4,
    "min": 8.01,
    "end": 8.28,
    "volume": 4634.57,
    "money": 38032.68
  },
  {
    "time": "2015-03-26",
    "start": 7.39,
    "max": 8.12,
    "min": 7.32,
    "end": 8.12,
    "volume": 4209.29,
    "money": 33643.03
  },
  {
    "time": "2015-03-25",
    "start": 7.36,
    "max": 7.6,
    "min": 7.2,
    "end": 7.38,
    "volume": 1845.49,
    "money": 13550.21
  },
  {
    "time": "2015-03-24",
    "start": 7.62,
    "max": 7.62,
    "min": 7.2,
    "end": 7.35,
    "volume": 2264.5,
    "money": 16699.5
  },
  {
    "time": "2015-03-23",
    "start": 7.54,
    "max": 7.68,
    "min": 7.46,
    "end": 7.59,
    "volume": 1834.28,
    "money": 13855.41
  },
  {
    "time": "2015-03-20",
    "start": 7.33,
    "max": 7.65,
    "min": 7.25,
    "end": 7.55,
    "volume": 2470.71,
    "money": 18588.13
  },
  {
    "time": "2015-03-19",
    "start": 7.38,
    "max": 7.66,
    "min": 7.26,
    "end": 7.37,
    "volume": 2450.54,
    "money": 18247.82
  },
  {
    "time": "2015-03-18",
    "start": 7.12,
    "max": 7.46,
    "min": 7.1,
    "end": 7.37,
    "volume": 2854.4,
    "money": 20828.88
  },
  {
    "time": "2015-03-17",
    "start": 6.95,
    "max": 7.13,
    "min": 6.87,
    "end": 7.09,
    "volume": 2457.13,
    "money": 17162.55
  },
  {
    "time": "2015-03-16",
    "start": 6.8,
    "max": 7.06,
    "min": 6.79,
    "end": 6.95,
    "volume": 1858.78,
    "money": 12924.21
  },
  {
    "time": "2015-03-13",
    "start": 6.85,
    "max": 6.93,
    "min": 6.69,
    "end": 6.79,
    "volume": 1167.06,
    "money": 7909.64
  },
  {
    "time": "2015-03-12",
    "start": 6.84,
    "max": 7.06,
    "min": 6.71,
    "end": 6.85,
    "volume": 2152.85,
    "money": 14835.41
  },
  {
    "time": "2015-03-11",
    "start": 6.98,
    "max": 7.04,
    "min": 6.77,
    "end": 6.84,
    "volume": 1445.77,
    "money": 9886.53
  },
  {
    "time": "2015-03-10",
    "start": 6.73,
    "max": 6.99,
    "min": 6.7,
    "end": 6.97,
    "volume": 1999.93,
    "money": 13770.37
  },
  {
    "time": "2015-03-09",
    "start": 6.59,
    "max": 6.88,
    "min": 6.4,
    "end": 6.72,
    "volume": 2243.1,
    "money": 14951.1
  },
  {
    "time": "2015-03-06",
    "start": 6.47,
    "max": 6.6,
    "min": 6.35,
    "end": 6.5,
    "volume": 1270.49,
    "money": 8229.96
  },
  {
    "time": "2015-03-05",
    "start": 6.43,
    "max": 6.54,
    "min": 6.34,
    "end": 6.47,
    "volume": 1363.09,
    "money": 8789.45
  },
  {
    "time": "2015-03-04",
    "start": 6.35,
    "max": 6.45,
    "min": 6.32,
    "end": 6.41,
    "volume": 1295.42,
    "money": 8265.63
  },
  {
    "time": "2015-03-03",
    "start": 6.16,
    "max": 6.47,
    "min": 6.07,
    "end": 6.42,
    "volume": 2266.82,
    "money": 14214.79
  },
  {
    "time": "2015-03-02",
    "start": 6.22,
    "max": 6.25,
    "min": 6.07,
    "end": 6.17,
    "volume": 1277.88,
    "money": 7850.34
  },
  {
    "time": "2015-02-27",
    "start": 6.16,
    "max": 6.33,
    "min": 6.15,
    "end": 6.19,
    "volume": 908.98,
    "money": 5663.74
  },
  {
    "time": "2015-02-26",
    "start": 6.12,
    "max": 6.18,
    "min": 6.1,
    "end": 6.16,
    "volume": 703.72,
    "money": 4328.56
  },
  {
    "time": "2015-02-25",
    "start": 6.09,
    "max": 6.18,
    "min": 6.03,
    "end": 6.12,
    "volume": 766.56,
    "money": 4678.73
  },
  {
    "time": "2015-02-17",
    "start": 6.11,
    "max": 6.15,
    "min": 6.06,
    "end": 6.08,
    "volume": 766.73,
    "money": 4677.31
  },
  {
    "time": "2015-02-16",
    "start": 6.03,
    "max": 6.14,
    "min": 6.01,
    "end": 6.11,
    "volume": 814.71,
    "money": 4948.33
  },
  {
    "time": "2015-02-13",
    "start": 5.98,
    "max": 6.34,
    "min": 5.93,
    "end": 6.08,
    "volume": 1992.56,
    "money": 12135.01
  },
  {
    "time": "2015-02-12",
    "start": 5.72,
    "max": 6.1,
    "min": 5.66,
    "end": 6.01,
    "volume": 2572.38,
    "money": 15312.73
  },
  {
    "time": "2015-02-11",
    "start": 5.69,
    "max": 5.77,
    "min": 5.67,
    "end": 5.72,
    "volume": 602.66,
    "money": 3443.99
  },
  {
    "time": "2015-02-10",
    "start": 5.46,
    "max": 5.75,
    "min": 5.43,
    "end": 5.73,
    "volume": 1298.63,
    "money": 7307.42
  },
  {
    "time": "2015-02-09",
    "start": 5.59,
    "max": 5.59,
    "min": 5.47,
    "end": 5.48,
    "volume": 435.98,
    "money": 2410.09
  },
  {
    "time": "2015-02-06",
    "start": 5.5,
    "max": 5.62,
    "min": 5.48,
    "end": 5.61,
    "volume": 630.6,
    "money": 3490.13
  },
  {
    "time": "2015-02-05",
    "start": 5.58,
    "max": 5.59,
    "min": 5.47,
    "end": 5.48,
    "volume": 636.7,
    "money": 3521.89
  },
  {
    "time": "2015-02-04",
    "start": 5.63,
    "max": 5.67,
    "min": 5.52,
    "end": 5.52,
    "volume": 635.38,
    "money": 3548.96
  },
  {
    "time": "2015-02-03",
    "start": 5.63,
    "max": 5.67,
    "min": 5.56,
    "end": 5.65,
    "volume": 434.34,
    "money": 2439.08
  },
  {
    "time": "2015-02-02",
    "start": 5.55,
    "max": 5.65,
    "min": 5.51,
    "end": 5.61,
    "volume": 338.71,
    "money": 1896.01
  },
  {
    "time": "2015-01-30",
    "start": 5.78,
    "max": 5.85,
    "min": 5.6,
    "end": 5.65,
    "volume": 574.74,
    "money": 3270.25
  },
  {
    "time": "2015-01-29",
    "start": 5.8,
    "max": 5.87,
    "min": 5.74,
    "end": 5.78,
    "volume": 605.55,
    "money": 3516.14
  },
  {
    "time": "2015-01-28",
    "start": 5.89,
    "max": 5.95,
    "min": 5.82,
    "end": 5.85,
    "volume": 653.47,
    "money": 3846.52
  },
  {
    "time": "2015-01-27",
    "start": 5.72,
    "max": 5.94,
    "min": 5.7,
    "end": 5.89,
    "volume": 1398.84,
    "money": 8194.18
  },
  {
    "time": "2015-01-26",
    "start": 5.65,
    "max": 5.73,
    "min": 5.58,
    "end": 5.72,
    "volume": 930.19,
    "money": 5247.01
  },
  {
    "time": "2015-01-23",
    "start": 5.68,
    "max": 5.72,
    "min": 5.6,
    "end": 5.62,
    "volume": 758.13,
    "money": 4284.8
  },
  {
    "time": "2015-01-22",
    "start": 5.49,
    "max": 5.78,
    "min": 5.41,
    "end": 5.71,
    "volume": 1139.94,
    "money": 6386.11
  },
  {
    "time": "2015-01-21",
    "start": 5.36,
    "max": 5.58,
    "min": 5.33,
    "end": 5.55,
    "volume": 701.11,
    "money": 3840.84
  },
  {
    "time": "2015-01-20",
    "start": 5.23,
    "max": 5.35,
    "min": 5.22,
    "end": 5.33,
    "volume": 817.97,
    "money": 4326.47
  },
  {
    "time": "2015-01-19",
    "start": 5.6,
    "max": 5.67,
    "min": 5.12,
    "end": 5.16,
    "volume": 1248.82,
    "money": 6669.96
  },
  {
    "time": "2015-01-16",
    "start": 5.67,
    "max": 5.73,
    "min": 5.66,
    "end": 5.69,
    "volume": 399.54,
    "money": 2274.94
  },
  {
    "time": "2015-01-15",
    "start": 5.6,
    "max": 5.67,
    "min": 5.57,
    "end": 5.67,
    "volume": 361.28,
    "money": 2031.66
  },
  {
    "time": "2015-01-14",
    "start": 5.62,
    "max": 5.69,
    "min": 5.61,
    "end": 5.62,
    "volume": 321.27,
    "money": 1812.93
  },
  {
    "time": "2015-01-13",
    "start": 5.64,
    "max": 5.71,
    "min": 5.58,
    "end": 5.65,
    "volume": 375.35,
    "money": 2120.87
  },
  {
    "time": "2015-01-12",
    "start": 5.79,
    "max": 5.79,
    "min": 5.58,
    "end": 5.6,
    "volume": 516.19,
    "money": 2921.05
  },
  {
    "time": "2015-01-09",
    "start": 5.95,
    "max": 5.97,
    "min": 5.8,
    "end": 5.82,
    "volume": 701.39,
    "money": 4123.5
  },
  {
    "time": "2015-01-08",
    "start": 5.95,
    "max": 6.06,
    "min": 5.91,
    "end": 5.97,
    "volume": 676.75,
    "money": 4056.12
  },
  {
    "time": "2015-01-07",
    "start": 6,
    "max": 6.04,
    "min": 5.92,
    "end": 5.96,
    "volume": 546.93,
    "money": 3267.16
  },
  {
    "time": "2015-01-06",
    "start": 5.89,
    "max": 6.09,
    "min": 5.84,
    "end": 6.07,
    "volume": 1169.3,
    "money": 6980.48
  },
  {
    "time": "2015-01-05",
    "start": 5.89,
    "max": 6,
    "min": 5.75,
    "end": 5.94,
    "volume": 806.1,
    "money": 4751.15
  }
];

