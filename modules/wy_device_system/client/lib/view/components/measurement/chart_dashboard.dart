import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:graphic/graphic.dart';
import 'package:intl/intl.dart';

import '../../../bloc/measurement_bloc.dart';
import '../../../bloc/targets_blocs.dart';
import '../../../bloc/wy_device_blocs.dart';
import '../../../model/measurement.dart';
import '../../../model/measurement_query.dart';
import '../../../repository/telemetry_repository.dart';
import '../../../services/measurement_websocket_service.dart';
import '../../monitor/monitor_page.dart';
import '../../ptz_control/ptz_control.dart';
import '../charts/measurement_chart.dart';
import 'query_panel.dart';
import '../charts/flexible-line-chart.dart';
import 'package:fl_chart/fl_chart.dart';

class ChartDashboard extends StatefulWidget {



  const ChartDashboard({Key? key}) : super(key: key);

  @override
  State<ChartDashboard> createState() => _ChartDashboardState();
}

class _ChartDashboardState extends State<ChartDashboard> {
  // 当前图表查询参数
  MeasurementType _currentChartType = MeasurementType.displacement;
  String? _selectedTargetId;
  bool _isRealtime = true;

  // 图表数据和控制器
  List<Map<String, dynamic>> _chartData = [];
  StreamSubscription? _chartBlocSubscription;
  final _changeDataController = StreamController<ChangeDataEvent<Map<String, dynamic>>>.broadcast();

  // 处理历史数据的定时器和时间范围
  DateTime _startTime = DateTime.now().subtract(const Duration(minutes: 10));
  DateTime _endTime = DateTime.now();

  @override
  void initState() {
    super.initState();

    // 订阅 MeasurementChartBloc 状态变化
    _setupMeasurementBlocListener();

  }

  @override
  void dispose() {
    _chartBlocSubscription?.cancel();
    _changeDataController.close();
    super.dispose();
  }

  // 设置 MeasurementChartBloc 监听器，在数据变化时更新图表
  void _setupMeasurementBlocListener() {
    final measurementChartBloc = context.read<MeasurementChartBloc>();
    _chartBlocSubscription = measurementChartBloc.telemetryStreamController.stream.listen((data) {
      //提取出当前选择的标靶数据
      debugPrint('Received data from WebSocket: $data');
      if (data.containsKey("Displacements")) {
        final displacements = data["Displacements"] as List;
        final targetData = displacements
            .where((item) => item['targetId'] == _selectedTargetId)
            .map((item) {
          // 将数据格式化为图表需要的格式
          return {
            'timestamp': DateTime.fromMillisecondsSinceEpoch(item['ts']),
            'sigmaX': item['sigmaX'],
            'sigmaY': item['sigmaY'],
          };
        }).toList();

        if (targetData.isNotEmpty) {
          _updateChartWithMeasurements(targetData);
        }
      }
    });

  }

  // 更新图表数据
  void _updateChartWithMeasurements(List<Map<String, dynamic>> measurements) {
    final len = measurements.length;
    if (measurements.isEmpty) return;

    _changeDataController.add(ChangeDataEvent(measurements));
    _chartData = measurements;
  }

  // 从查询参数更新图表
  Future<void> _handleQueryChanged(MeasurementQuery query) async {
    // 更新状态
    setState(() {
      _currentChartType = query.chartType;
      _selectedTargetId = query.selectedTargetId;
      _isRealtime = query.isRealtime;
      _startTime = query.startTime;
      _endTime = query.endTime;
    });


    if (_isRealtime) {

    } else {

      // 从 MeasurementChartBloc 加载历史数据
      final types = _getDataFieldsByChartType(_currentChartType);
      String deviceId = context.read<WyDeviceBloc>().state.deviceId;

      final telemetryRepository = TelemetryRepository();
      final startTs = _startTime.millisecondsSinceEpoch;
      final endTs = _endTime.millisecondsSinceEpoch;
      List<Map<String, dynamic>> result = await telemetryRepository.queryDisplacement(
        deviceId,
        targetId: _selectedTargetId,
        startTs: startTs,
        endTs: endTs,
      );
      List<Map<String, dynamic>> modifiableResult = [];
      // 分批处理数据以防止UI卡顿
      const int batchSize = 5000; // 每批处理的数据量
      int processedCount = 0;

      // 处理第一批数据立即显示
      for (var i = 0; i < min(batchSize, result.length); i++) {
        var row = result[i];
        Map<String, dynamic> newRow = Map<String, dynamic>.from(row);
        newRow['timestamp'] = DateTime.fromMillisecondsSinceEpoch(row['ts']);
        modifiableResult.add(newRow);
        processedCount++;
      }

    // 先渲染第一批数据
      if (modifiableResult.isNotEmpty) {
        _changeDataController.add(ChangeDataEvent(modifiableResult));
        // setState(() {
        //   _chartData.addAll(modifiableResult);
        // });
        debugPrint('首批渲染数据: ${modifiableResult.length}');
      }

    // 异步处理剩余数据
      if (processedCount < result.length) {
        Future.delayed(Duration(milliseconds: 500), () {
          _processBatchData(result, processedCount, batchSize);
        });
      }

    }
  }

  // 递归处理批量数据
  void _processBatchData(List<Map<String, dynamic>> result, int startIndex, int batchSize) {
    final List<Map<String, dynamic>> currentBatch = [];
    final endIndex = min(startIndex + batchSize, result.length);

    for (var i = startIndex; i < endIndex; i++) {
      var row = result[i];
      Map<String, dynamic> newRow = Map<String, dynamic>.from(row);
      newRow['timestamp'] = DateTime.fromMillisecondsSinceEpoch(row['ts']);
      currentBatch.add(newRow);
    }

    if (currentBatch.isNotEmpty) {
      // setState(() {
      //   _chartData.addAll(currentBatch);
      // });
      _changeDataController.add(ChangeDataEvent(_chartData));
      debugPrint('增量渲染数据: ${currentBatch.length}, 总数: ${_chartData.length}');
    }

    // 如果还有更多数据，继续处理下一批
    if (endIndex < result.length) {
      Future.delayed(Duration(milliseconds: 100), () {
        _processBatchData(result, endIndex, batchSize);
      });
    } else {
      debugPrint('全部数据渲染完成: ${_chartData.length}');
    }
  }

  // 根据图表类型获取对应的数据字段
  List<String> _getDataFieldsByChartType(MeasurementType type) {
    switch (type) {
      case MeasurementType.displacement:
        return ['displacement_x', 'displacement_y'];
      case MeasurementType.envStatus:
        return ['temperature', 'humidity', 'pressure'];
      case MeasurementType.imu:
        return ['accel_x', 'accel_y', 'accel_z'];
      case MeasurementType.acceleration:
        return ['accel_magnitude'];
      case MeasurementType.multiSensor:
        return ['temperature', 'humidity', 'accel_magnitude', 'displacement_magnitude'];
    }
  }



  // 根据图表类型获取对应的图表配置
  Widget _buildChartByType() {

    switch (_currentChartType) {
      case MeasurementType.displacement:
        return SensorChart(
          title: '位移(mm)',
          xField: 'timestamp',
          yFields: ['sigmaX', 'sigmaY'],
          labels: ['X', 'Y'],
          colors: [Colors.blue, Colors.red],
          units: ['mm','mm'],
          changeDataStreamController: null,
          timeFormat: 'MM-dd HH:mm:ss',
          autoRefreshInterval: Duration(seconds: 1),
          dataLoader: () {
            return Future.value(_chartData);

          },
        );

      case MeasurementType.envStatus:
        return SensorChart(
          title: '环境状态监测',
          xField: 'timestamp',
          yFields: ['temperature', 'humidity', 'pressure'],
          labels: ['温度', '湿度', '气压'],
          colors: [Colors.red, Colors.blue, Colors.purple],
          units: ['°C', '%', 'hPa'],
          shareYDomain: false, // 不同单位，不共享Y轴
            changeDataStreamController: _changeDataController
        );

      case MeasurementType.imu:
        return SensorChart(
          title: 'IMU 数据',
          xField: 'timestamp',
          yFields: ['accel_x', 'accel_y', 'accel_z'],
          labels: ['X 轴', 'Y 轴', 'Z 轴'],
          colors: [Colors.orange, Colors.green, Colors.indigo],
          units: ['g', 'g', 'g'],
            changeDataStreamController: _changeDataController
        );

      default:
        return const Center(child: Text('未支持的图表类型'));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 查询面板
          QueryPanel(
            onQueryChanged: _handleQueryChanged,
          ),

          // 图表标题和状态指示
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Row(
              children: [
                Text(
                  _isRealtime ? '实时数据监测' : '历史数据查询',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(width: 8),
                if (_isRealtime)
                  Container(
                    width: 10,
                    height: 10,
                    decoration: BoxDecoration(
                      color: Colors.green,
                      shape: BoxShape.circle,
                    ),
                  ),
                const Spacer(),
                if (_selectedTargetId != null)
                  Chip(
                    label: Text('标靶: $_selectedTargetId'),
                    backgroundColor: Colors.blue.withOpacity(0.1),
                  ),
              ],
            ),
          ),

          // 图表区域
          Expanded(
            child: Container(
              constraints: const BoxConstraints(
                maxWidth: 500,
                minHeight: 200,
              ),
              // child: _buildChartByType()
              // child: MeasurementVisualizer(
              //   targets: context.read<TargetBloc>().state.targets,
              //   measurementChartBloc: context.read<MeasurementChartBloc>(),
              //
              // )
              // child:LargeDatasetGraphicChart()
              // child:LargeDatasetMultiSensorChart()
              // child: SpeedChart()
              child:
              RepaintBoundary(
                child:_buildChartByType()
              )


            ),
          ),
        ],
      ),
    );
  }
}

