import 'package:client/model/wy_device.dart';
import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class SettingPwm extends StatefulWidget {
  final WyDeviceAttribute? deviceAttribute;
  final onConfirm;
  const SettingPwm({super.key, this.deviceAttribute, required this.onConfirm});

  @override
  State<SettingPwm> createState() => _SettingPwmState();
}

class _SettingPwmState extends State<SettingPwm> {
  late int pwm;
  late bool pwSelfAdaptive;
  late int wyReportSelfAdaptiveInterval;
  late int minimumAdaptivePwm;
  late int maximumAdaptivePwm;
  @override
  void initState() {
    super.initState();
    pwm = widget.deviceAttribute!.pwm;
    pwSelfAdaptive = widget.deviceAttribute!.pwSelfAdaptive;
    wyReportSelfAdaptiveInterval =
        widget.deviceAttribute!.wyReportSelfAdaptiveInterval;
    minimumAdaptivePwm = widget.deviceAttribute!.minimumAdaptivePwm;
    maximumAdaptivePwm = widget.deviceAttribute!.maximumAdaptivePwm;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    return SizedBox(
      width: screenSize.width * 0.88,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.fromLTRB(12, 0, 12, 0),
            alignment: Alignment.centerLeft,
            height: 60,
            child: TDText(
              '补光灯设置',
              font: TDTheme.of(context).fontTitleExtraLarge,
            ),
          ),
          Column(
            children: [
              TDCell(
                hover: false,
                title: '补光灯',
                style: TDCellStyle(
                    padding: const EdgeInsets.fromLTRB(18, 0, 0, 0)),
                noteWidget: Container(
                  alignment: Alignment.centerRight,
                  width: 200,
                  child: DropdownButton2(

                    onChanged: (value) {
                      setState(() {
                        pwm = value.toInt();
                      });
                    },
                  ),
                ),
              ),
              TDCell(
                hover: false,
                title: '补光灯亮度',
                style: TDCellStyle(
                    padding: const EdgeInsets.fromLTRB(18, 0, 0, 0)),
                noteWidget: Container(
                  alignment: Alignment.centerRight,
                  width: 200,
                  child: TDSlider(
                    sliderThemeData: TDSliderThemeData(
                      context: context,
                      showThumbValue: true,
                      min: 0,
                      max: 10000,
                      scaleFormatter: (value) => value.round().toString(),
                    ),
                    value: pwm.toDouble(),
                    onChanged: (value) {
                      setState(() {
                        pwm = value.toInt();
                      });
                    },
                  ),
                ),
              ),
              TDCell(
                hover: false,
                title: 'PWM自适应',
                noteWidget: TDSwitch(
                  isOn: pwSelfAdaptive,
                  onChanged: (value) {
                    setState(() {
                      pwSelfAdaptive = value;
                    });
                    return value;
                  },
                ),
              ),
              TDCell(
                hover: false,
                title: '自适应上报间隔 (秒)',
                noteWidget: TDStepper(
                    size: TDStepperSize.large,
                    theme: TDStepperTheme.outline,
                    value: wyReportSelfAdaptiveInterval,
                    min: 0,
                    max: 10000),
              ),
              TDCell(
                hover: false,
                title: '最小自适应PWM',
                noteWidget: TDStepper(
                    disabled: !pwSelfAdaptive,
                    size: TDStepperSize.large,
                    theme: TDStepperTheme.outline,
                    value: minimumAdaptivePwm,
                    min: 0,
                    max: 10000),
              ),
              TDCell(
                hover: false,
                title: '最大自适应PWM',
                noteWidget: TDStepper(
                    disabled: !pwSelfAdaptive,
                    size: TDStepperSize.large,
                    theme: TDStepperTheme.outline,
                    value: maximumAdaptivePwm,
                    min: 0,
                    max: 10000),
              ),
            ],
          ),
          Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.fromLTRB(24, 24, 24, 24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TDButton(
                    text: '取消',
                    theme: TDButtonTheme.light,
                    isBlock: true,
                    onTap: () {
                      Navigator.pop(context);
                    },
                  ),
                  TDButton(
                    text: '确定',
                    theme: TDButtonTheme.primary,
                    isBlock: true,
                    onTap: () {
                      widget.onConfirm({
                        'pwm': pwm,
                        'pwSelfAdaptive': pwSelfAdaptive,
                        'wyReportSelfAdaptiveInterval':
                            wyReportSelfAdaptiveInterval,
                        'minimumAdaptivePwm': minimumAdaptivePwm,
                        'maximumAdaptivePwm': maximumAdaptivePwm
                      });
                    },
                  )
                ],
              ))
        ],
      ),
    );
  }
}
