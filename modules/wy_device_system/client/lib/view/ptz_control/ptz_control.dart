import 'package:flutter/material.dart';

class CameraPTZController extends StatefulWidget {
  /// 按钮点击回调，传入方向参数
  final Function(String direction, bool isPressed) onDirectionControl;

  /// 是否启用长按持续移动功能
  final bool enableContinuousMove;

  /// 云台移动步长，默认为 5
  final int stepSize;

  /// 是否启用控制
  final bool enabled;

  /// 按钮大小
  final double buttonSize;

  const CameraPTZController({
    Key? key,
    required this.onDirectionControl,
    this.enableContinuousMove = true,
    this.stepSize = 5,
    this.enabled = true,
    this.buttonSize = 54.0,
  }) : super(key: key);

  @override
  State<CameraPTZController> createState() => _CameraPTZControllerState();
}

class _CameraPTZControllerState extends State<CameraPTZController> {
  // 当前按下的方向按钮
  String? _activeDirection;

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final buttonColor = isDarkMode ? Colors.grey[800] : Colors.grey[200];
    final iconColor = isDarkMode ? Colors.white : Colors.black87;
    final disabledColor = isDarkMode ? Colors.grey[900] : Colors.grey[100];
    final disabledIconColor = Colors.grey;
    final activeColor = Colors.blue.withOpacity(0.6);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[900] : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode ? Colors.grey[800]! : Colors.grey[300]!,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '云台控制',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.grey[300] : Colors.grey[800],
            ),
          ),
          const SizedBox(height: 16),
          // 上方向按钮
          _buildDirectionButton(
            icon: Icons.keyboard_arrow_up,
            direction: 'up',
            buttonColor: buttonColor,
            iconColor: iconColor,
            disabledColor: disabledColor,
            disabledIconColor: disabledIconColor,
            activeColor: activeColor,
          ),
          const SizedBox(height: 8),
          // 左右方向按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildDirectionButton(
                icon: Icons.keyboard_arrow_left,
                direction: 'left',
                buttonColor: buttonColor,
                iconColor: iconColor,
                disabledColor: disabledColor,
                disabledIconColor: disabledIconColor,
                activeColor: activeColor,
              ),
              SizedBox(width: widget.buttonSize), // 按钮之间的间距
              _buildDirectionButton(
                icon: Icons.keyboard_arrow_right,
                direction: 'right',
                buttonColor: buttonColor,
                iconColor: iconColor,
                disabledColor: disabledColor,
                disabledIconColor: disabledIconColor,
                activeColor: activeColor,
              ),
            ],
          ),
          const SizedBox(height: 8),
          // 下方向按钮
          _buildDirectionButton(
            icon: Icons.keyboard_arrow_down,
            direction: 'down',
            buttonColor: buttonColor,
            iconColor: iconColor,
            disabledColor: disabledColor,
            disabledIconColor: disabledIconColor,
            activeColor: activeColor,
          ),
          const SizedBox(height: 16),
          // 步长显示
          Row(
            children: [
              Text(
                '步长: ${widget.stepSize}',
                style: TextStyle(
                  fontSize: 14,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDirectionButton({
    required IconData icon,
    required String direction,
    required Color? buttonColor,
    required Color iconColor,
    required Color? disabledColor,
    required Color disabledIconColor,
    required Color activeColor,
  }) {
    final bool isActive = _activeDirection == direction;
    final effectiveColor = isActive ? activeColor : (widget.enabled ? buttonColor : disabledColor);

    return GestureDetector(
      onTapDown: widget.enabled ? (_) => _handleDirectionPressed(direction) : null,
      onTapUp: widget.enabled ? (_) => _handleDirectionReleased(direction) : null,
      onTapCancel: widget.enabled ? () => _handleDirectionReleased(direction) : null,
      child: Material(
        color: effectiveColor,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          width: widget.buttonSize,
          height: widget.buttonSize,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: widget.enabled ? iconColor : disabledIconColor,
            size: widget.buttonSize * 0.5,
          ),
        ),
      ),
    );
  }

  void _handleDirectionPressed(String direction) {
    setState(() {
      _activeDirection = direction;
    });
    widget.onDirectionControl(direction, true);
  }

  void _handleDirectionReleased(String direction) {
    if (_activeDirection == direction) {
      setState(() {
        _activeDirection = null;
      });
      widget.onDirectionControl(direction, false);
    }
  }
}
