// device_discovery_page.dart
import 'package:client/view/device_discovery/device_item.dart';
import 'package:flutter/material.dart';
import '../../model/discovered_device.dart';

class DeviceSearchDelegate extends SearchDelegate<DiscoveredDevice?> {
  final List<DiscoveredDevice> devices;
  final Function(BuildContext, DiscoveredDevice) onDeviceSelected;
  final Function(DiscoveredDevice) onDeviceDeleted;

  DeviceSearchDelegate({
    required this.devices,
    required this.onDeviceSelected,
    required this.onDeviceDeleted,
  });
  List<DiscoveredDevice> filteredDevices = [];
  @override
  String get searchFieldLabel => '搜索设备名称或IP地址';

  @override
  TextStyle? get searchFieldStyle => const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.normal,
      );

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      if (query.isNotEmpty)
        IconButton(
          icon: const Icon(Icons.clear),
          tooltip: '清除',
          onPressed: () {
            query = '';
          },
        ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      tooltip: '返回',
      onPressed: () {
        close(context, null);
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return _buildSearchResults(context);
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return _buildSearchResults(context);
  }

  Widget _buildSearchResults(BuildContext context) {
    // 过滤设备
    filteredDevices = devices.where((device) {
      if (query.isEmpty) return true;
      final lowerQuery = query.toLowerCase();
      final str = '${device.name}${device.ipAddress}${device.id}';
      return str.toLowerCase().contains(lowerQuery);
    }).toList();
    if (filteredDevices.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              query.isEmpty ? '请输入搜索关键词' : '未找到匹配的设备',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
        maxCrossAxisExtent: 400, // 卡片最大宽度
        mainAxisSpacing: 12.0, // 垂直间距
        crossAxisSpacing: 12.0, // 水平间距
        mainAxisExtent: 170,
      ),
      itemCount: filteredDevices.length,
      itemBuilder: (context, index) {
        final device = filteredDevices[index];
        return DeviceItem(
            isSearch: true,
            device: devices[index],
            onConnect: () => onDeviceSelected(context, device),
            onDelete: () => onDeviceDeleted(device));
      },
    );
  }
}
