import 'package:client/model/discovered_device.dart';
import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class DeviceItem extends StatefulWidget {
  final device;
  final onConnect;
  final onDelete;
  final dynamic onSave;
  bool isSearch = false;
  DeviceItem({
    super.key,
    required this.device,
    required this.onConnect,
    required this.onDelete,
    this.isSearch = false,
    this.onSave,
  });

  @override
  State<DeviceItem> createState() => _DeviceItemState();
}

class _DeviceItemState extends State<DeviceItem> {
  // 删除设备弹窗
  void _confirmDeleteDevice(BuildContext context, DiscoveredDevice device) {
    showGeneralDialog(
      context: context,
      pageBuilder: (BuildContext context, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        return TDAlertDialog(
          title: '删除设备',
          content: '确定要删除设备 "${device.name}" 吗？',
          rightBtnAction: () {
            widget.onDelete();
            Navigator.pop(context);
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final device = widget.device;
    // 是否手动添加
    final bool isManuallyAdded = device.id.startsWith('manual_');
    return Card(
      color: Colors.white,
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: InkWell(
          borderRadius: const BorderRadius.all(Radius.circular(8.0)),
          onTap: () => widget.onConnect(),
          child: Stack(
            children: [
              if (!device.isSaved)
                const Positioned(
                  top: 12,
                  right: 0,
                  child: TDTag(
                    '新',
                    isLight: true,
                    theme: TDTagTheme.success,
                  ),
                ),
              Padding(
                padding: const EdgeInsets.fromLTRB(12, 12, 12, 4),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      spacing: 12,
                      children: [
                        TDAvatar(
                          size: TDAvatarSize.medium,
                          type: TDAvatarType.customText,
                          text: isManuallyAdded ? 'M' : 'W',
                        ),
                        if (isManuallyAdded)
                          const TDTag(
                            '手动添加',
                            isLight: true,
                            theme: TDTagTheme.warning,
                          ),
                        Expanded(
                          child: Text(
                            device.name,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: TDText(
                            '类型：视觉位移计',
                            font: TDTheme.of(context).fontBodyMedium,
                            textColor: TDTheme.of(context).fontGyColor2,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        )
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: TDText(
                            '地址：${device.ipAddress}',
                            font: TDTheme.of(context).fontBodyMedium,
                            textColor: TDTheme.of(context).fontGyColor2,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        )
                      ],
                    ),
                    Align(
                      alignment: Alignment.bottomRight,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (!widget.isSearch)
                            IconButton(
                              icon: Icon(
                                device.isSaved
                                    ? TDIcons.bookmark_checked
                                    : TDIcons.bookmark_add,
                                size: 18,
                                color: device.isSaved
                                    ? TDTheme.of(context).warningNormalColor
                                    : null,
                              ),
                              tooltip: device.isSaved ? '取消保存' : '保存设备',
                              onPressed: widget.onSave,
                            ),
                          IconButton(
                            icon: const Icon(TDIcons.map_connection, size: 18),
                            color: TDTheme.of(context).brandNormalColor,
                            tooltip: '连接设备',
                            onPressed: () => widget.onConnect(),
                          ),
                          IconButton(
                            icon: const Icon(TDIcons.delete, size: 18),
                            color: TDTheme.of(context).errorNormalColor,
                            tooltip: '删除设备',
                            onPressed: () =>
                                _confirmDeleteDevice(context, device),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ],
          )),
    );
  }
}
